<?php

namespace Modules\Cargo\Http\Controllers;

use App\Imports\ShipmentsImport;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Cargo\Http\DataTables\ShipmentsDataTable;
use Modules\Cargo\Http\Requests\ShipmentRequest;
use Modules\Cargo\Entities\Shipment;
use Modules\Cargo\Entities\ShipmentChat;
use Modules\Cargo\Entities\ShipmentSetting;
use Modules\Cargo\Entities\ClientPackage;
use Modules\Cargo\Entities\Client;
use Modules\Cargo\Entities\ClientStateCost;
use Modules\Cargo\Entities\Package;
use Modules\Cargo\Entities\Cost;
use Modules\Cargo\Http\Helpers\ShipmentPRNG;
use Modules\Cargo\Http\Helpers\MissionPRNG;
use Modules\Cargo\Entities\PackageShipment;
use Modules\Cargo\Http\Helpers\ShipmentActionHelper;
use Modules\Cargo\Http\Helpers\StatusManagerHelper;
use Modules\Cargo\Http\Helpers\TransactionHelper;
use Modules\Cargo\Entities\Mission;
use Modules\Cargo\Entities\ShipmentMission;
use Modules\Cargo\Entities\ShipmentReason;
use Modules\Cargo\Entities\Country;
use Modules\Cargo\Entities\State;
use Modules\Cargo\Entities\Area;
use Modules\Cargo\Entities\ClientAddress;
use Modules\Cargo\Entities\DeliveryTime;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\BusinessSetting;
use Modules\Cargo\Utility\CSVUtility;
use DB;
use Modules\Cargo\Http\Helpers\UserRegistrationHelper;
use app\Http\Helpers\ApiHelper;
use App\Http\Resources\ChatResource;
use App\Http\Resources\ShipmentResource;
use App\Imports\ShipmentsAPIImport;
use App\Models\User;
use Modules\Cargo\Events\AddShipment;
use Modules\Cargo\Events\CreateMission;
use Modules\Cargo\Events\ShipmentAction;
use Modules\Cargo\Events\UpdateMission;
use Modules\Cargo\Events\UpdateShipment;
use Modules\Acl\Repositories\AclRepository;
use Modules\Cargo\Http\Controllers\ClientController;
use Modules\Cargo\Http\Requests\RegisterRequest;
use Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Session;
use Maatwebsite\Excel\Excel as ExcelExcel;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Cargo\Entities\ContactUs;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ShipmentController extends Controller
{
    private $aclRepo;

    public function __construct(AclRepository $aclRepository)
    {
        $this->aclRepo = $aclRepository;
        // check on permissions
        $this->middleware('user_role:1|0|3|4')->only('index', 'shipmentsReport', 'create');
        $this->middleware('user_role:4')->only('ShipmentApis');
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(ShipmentsDataTable $dataTable, $status = 'all', $type = null)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.shipments')
            ]
        ]);
        Session::put('date_error', 0);
        Session::put('errors2', null);
        Session::put('fails_rows_array_with_errors', null);

         Session::put('status',  $status  );

        $actions = new ShipmentActionHelper();
        if ($status == 'all') {
            $actions = $actions->get('all');
        } else {
            $actions = $actions->get($status, $type);
        }

        $data_with = ['actions' => $actions, 'status' => $status];
        $share_data = array_merge(get_class_vars(ShipmentsDataTable::class), $data_with);

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::' . $adminTheme . '.pages.shipments.index', $share_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.shipments'),
                'path' => fr_route('shipments.index')
            ],
            [
                'name' => __('cargo::view.add_shipment'),
            ],
        ]);

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        $order_id_validation = 'nullable';

        // Normalize phone numbers before validation
        if (isset($request->Shipment['client_phone'])) {
            $originalPhone = $request->Shipment['client_phone'];
            $normalizedPhone = $this->normalizePhoneNumber($originalPhone);

            // If phone is empty after normalization, keep it empty for validation to catch
            $request->merge([
                'Shipment' => array_merge($request->Shipment, [
                    'client_phone' => $normalizedPhone
                ])
            ]);
        }

        if (isset($request->Shipment['reciver_phone'])) {
            $originalPhone = $request->Shipment['reciver_phone'];
            $normalizedPhone = $this->normalizePhoneNumber($originalPhone);

            $request->merge([
                'Shipment' => array_merge($request->Shipment, [
                    'reciver_phone' => $normalizedPhone
                ])
            ]);
        }

        $request->validate([
            'Shipment.type'            => 'required',
            'Shipment.branch_id'       => 'required',
            'Shipment.shipping_date'   => 'nullable',
            'Shipment.collection_time' => 'nullable',
            'Shipment.client_id'       => 'required',
            'Shipment.client_phone'    => 'nullable|min:7', // Made nullable to handle client users
            'Shipment.follow_up_country_code'    => 'nullable',
            'Shipment.client_address'  => 'required',
            'Shipment.reciver_name'    => 'required|string|min:3|max:50',
            'Shipment.reciver_phone'   => 'required|min:7', // Made more flexible
            'Shipment.country_code'    => 'nullable',
            'Shipment.reciver_address' => 'required|string|min:8',
            //  'Shipment.from_country_id' => 'required',
            //  'Shipment.to_country_id'   => 'required',
            //  'Shipment.from_state_id'   => 'required',
            'Shipment.to_state_id'     => 'required',
            // 'Shipment.from_area_id'    => 'required',
            'Shipment.to_area_id'      => 'required',
            'Shipment.payment_type'    => 'required',
            'Shipment.payment_method_id' => 'required',
            'Shipment.order_id'          => $order_id_validation,
            'Shipment.attachments_before_shipping' => 'nullable',
            'Shipment.amount_to_be_collected'      => 'required',
            // 'Shipment.delivery_time'    => 'nullable',
            'Shipment.total_weight'     => 'required',
        ]);


        // dd($request->Shipment['shipping_date']);
        // Calculating "delivery time"  for The shipment is automatic
        $shippingDate = $request->Shipment['collection_time'];
        $collectionTime = $request->Shipment['shipping_date'];

        $shippingDate = date("H:i:s", strtotime($shippingDate));
        $collectionTime = $collectionTime;
        $shippingDateTime = Carbon::parse($shippingDate . ' ' . $collectionTime);
        $currentDateTime = Carbon::now();

        $deliveryTime = $currentDateTime->diffForHumans($shippingDateTime, true);

        $request->merge(['Shipment' => array_merge($request->Shipment, ['delivery_time' => $deliveryTime])]);

        try {
            DB::beginTransaction();
            $model = $this->storeShipment($request);
            $model->addFromMediaLibraryRequest($request->image)->toMediaCollection('attachments');
            event(new AddShipment($model));
            DB::commit();
            return redirect()->route('shipments.show', $model->id)->with(['message_alert' => __('cargo::messages.created')]);
        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
            exit;

            flash(translate("Error"))->error();
            return back();
        }
    }

    private function storeShipment($request, $token = null)
    {
        $model = new Shipment();
        $model->fill($request->Shipment);
        $model->code = -1;
        $model->status_id = Shipment::SAVED_STATUS;
        $date = date_create();
        $today = date("Y-m-d");

        if (auth()->user() && auth()->user()->role == 4) { // IF IN AUTH USER == CLIENT
            $client = Client::where('user_id', auth()->user()->id)->first();
            $model->client_id = $client->id;

            $model['client_id'] = $client->id;
            $model['client_phone'] = $client->responsible_mobile;
            $model['client_address'] = $client->addressess->first()?->id ?? 0;
            $model['from_state_id'] = $client->state_id ?? 0;
            $model['from_area_id'] = $client->area_id ?? 0;
            $model['branch_id'] = Branch::first()?->id ?? 0;

            $model['payment_method_id'] = 'cash_payment';
            $model['payment_type'] = 2;
            $model['from_country_id'] = 65;
            $model['to_country_id'] = 65;
        }



        if (isset($token)) {

            $user = User::where('remember_token', $token)->first();
            $userClient = Client::where('user_id', $user->id)->first();

            if (isset($user)) {
                $model->client_id = $userClient->id;

                $model['client_id'] = $userClient->id;
                $model['client_phone'] = $userClient->responsible_mobile;
                $model['client_address'] = $userClient->addressess->first()?->id ?? 0;
                $model['from_state_id'] = $userClient->state_id ?? 0;
                $model['from_area_id'] = $userClient->area_id ?? 0;
                $model['branch_id'] = Branch::first()?->id ?? 0;

                $model['payment_method_id'] = 'cash_payment';
                $model['payment_type'] = 2;
                $model['from_country_id'] = 65;
                $model['to_country_id'] = 65;

                // Validation
                if (!isset($request->Shipment['type']) || !isset($request->Shipment['branch_id']) || !isset($request->Shipment['shipping_date']) || !isset($request->Shipment['client_address']) || !isset($request->Shipment['reciver_name']) || !isset($request->Shipment['reciver_phone']) || !isset($request->Shipment['reciver_address']) || !isset($request->Shipment['from_country_id']) || !isset($request->Shipment['to_country_id']) || !isset($request->Shipment['from_state_id']) || !isset($request->Shipment['to_state_id']) || !isset($request->Shipment['from_area_id']) || !isset($request->Shipment['to_area_id']) || !isset($request->Shipment['payment_method_id']) || !isset($request->Shipment['payment_type']) || !isset($request->Package)) {
                    // $message = 'Please make sure to add all required fields';
                    // return $message;
                } else {
                    // if ($request->Shipment['type'] != Shipment::POSTPAID && $request->Shipment['type'] != Shipment::PREPAID) {
                    //     return 'Invalid Type';
                    // }

                    // if (!Branch::find($request->Shipment['branch_id'])) {
                    //     return 'Invalid Branch';
                    // }

                    // if (!ClientAddress::where('client_id', $userClient->id)->where('id', $request->Shipment['client_address'])->first()) {
                    //     return 'Invalid Client Address';
                    // }

                    // if (!Country::where('covered', 1)->where('id', $request->Shipment['from_country_id'])->first() || !Country::where('covered', 1)->where('id', $request->Shipment['to_country_id'])->first()) {
                    //     return 'Invalid Country';
                    // }

                    // if (!State::where('covered', 1)->where('id', $request->Shipment['from_state_id'])->first() || !State::where('covered', 1)->where('id', $request->Shipment['to_state_id'])->first()) {
                    //     return 'Invalid State';
                    // }

                    // if (!Area::where('state_id', $request->Shipment['from_state_id'])->where('id', $request->Shipment['from_area_id'])->first() || !Area::where('state_id', $request->Shipment['to_state_id'])->where('id', $request->Shipment['to_area_id'])->first()) {
                    //     return 'Invalid Area';
                    // }

                    // if (isset($request->Shipment['payment_method_id'])) {
                    //     $paymentSettings = resolve(\Modules\Payments\Entities\PaymentSetting::class)->toArray();
                    //     if (!isset($paymentSettings[$request->Shipment['payment_method_id']])) {
                    //         return 'Invalid Payment Method Id';
                    //     }
                    // }

                    // if ($request->Shipment['payment_type'] != Shipment::POSTPAID && $request->Shipment['payment_type'] != Shipment::PREPAID) {
                    //     return 'Invalid Payment Type';
                    // }

                    // if(isset($request->Shipment['delivery_time'])){
                    //     $delivery_time = DeliveryTime::where('id', $request->Shipment['delivery_time'] )->first();
                    //     if(!$delivery_time){
                    //         return 'Invalid Delivery Time';
                    //     }
                    // }

                }

                if (!isset($request->Shipment['client_phone'])) {
                    $model->client_phone = $userClient->responsible_mobile;
                }

                if (!isset($request->Shipment['amount_to_be_collected'])) {
                    $model->amount_to_be_collected = 0;
                }
            } else {
                return response()->json(['message' => 'invalid or Expired Api Key']);
            }
        }

        if (!$model->save()) {
            return response()->json(['message' => new \Exception()]);
        }

        if (ShipmentSetting::getVal('def_shipment_code_type') == 'random') {
            $barcode = ShipmentPRNG::get();
        } else {
            $code = '';
            for ($n = 0; $n < ShipmentSetting::getVal('shipment_code_count'); $n++) {
                $code .= '0';
            }
            $code       =   substr($code, 0, -strlen($model->id));
            $barcode    =   $code . $model->id;
        }
        $model->barcode = $barcode;
        $model->code = ShipmentSetting::getVal('shipment_prefix') . $barcode;

        if (auth()->user() && auth()->user()->role == 4) { // IF IN AUTH USER == CLIENT
            $client = Client::where('user_id', auth()->user()->id)->first();
            $model->client_id = $client->id;
        }

        if (!$model->save()) {
            return response()->json(['message' => new \Exception()]);
        }

        $costs = $this->applyShipmentCostNew($model, $request->Package);

        $model->fill($costs);
        if (!$model->save()) {
            return response()->json(['message' => new \Exception()]);
        }

        $counter = 0;
        if (isset($request->Package)) {
            if (!empty($request->Package)) {

                if (isset($request->Package[$counter]['package_id'])) {

                    if (isset($token)) {
                        $total_weight = 0;
                    }

                    foreach ($request->Package as $package) {
                        if (isset($token)) {
                            if (!Package::find($package['package_id'])) {
                                return 'Package invalid';
                            }

                            if (!isset($package['qty'])) {
                                $package['qty'] = 1;
                            }

                            if (!isset($package['weight'])) {
                                $package['weight'] = 1;
                            }
                            if (!isset($package['length'])) {
                                $package['length'] = 1;
                            }
                            if (!isset($package->width)) {
                                $package['width'] = 1;
                            }
                            if (!isset($package['height'])) {
                                $package['height'] = 1;
                            }

                            $total_weight = $total_weight + $package['weight'];
                        }
                        $package_shipment = new PackageShipment();
                        $package_shipment->fill($package);
                        $package_shipment->shipment_id = $model->id;
                        if (!$package_shipment->save()) {
                            throw new \Exception();
                        }
                    }

                    if (isset($token)) {
                        $model->total_weight = $total_weight;
                        if (!$model->save()) {
                            return response()->json(['message' => new \Exception()]);
                        }
                    }
                }
            }
        }

        if (isset($token)) {
            $message = 'Shipment added successfully';
            return $message;
        } else {
            return $model;
        }
    }

    public function storeAPI(Request $request)
    {
        try {
            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if ($user) {
                DB::beginTransaction();
                $message = $this->storeShipment($request, $request->header('token'));
                DB::commit();
                return response()->json(['message' => $message]);
            } else {
                return response()->json(['message' => 'Not Authorized']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return $e->getMessage();
        }
    }


    public function deleteAPI(Request $request)
    {
        $request->validate([
            'shipment_id' => 'required',
        ]);

        try {
            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if ($user) {
                Shipment::where('id', $request->shipment_id)->delete();

                $message = 'Shipment deleted successfully';
                return response()->json(['message' => $message]);
            } else {
                return response()->json(['message' => 'Not Authorized']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return $e->getMessage();
        }
    }


    public function getShipmentsAPI(Request $request)
    {
        try {
            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if ($user) {
                $userClient = Client::where('user_id', $user->id)->first();
                $shipments = new Shipment();

                $shipments = $shipments->where('client_id', $userClient->id);
                if (isset($request->code) && !empty($request->code)) {
                    $shipments = $shipments->where('code', $request->code);
                }
                if (isset($request->type) && !empty($request->type)) {
                    $shipments = $shipments->where('type', $request->type);
                }

                $shipments = $shipments->with(['pay', 'from_address'])->orderBy('client_id')->orderBy('id', 'DESC')->paginate(20);
                return response()->json($shipments);
            } else {
                return response()->json(['message' => 'Not Authorized']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return $e->getMessage();
        }
    }

    public function getShipmentsAPINew(Request $request)
    {
        try {
            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if ($user) {
                $userClient = Client::where('user_id', $user->id)->first();
                $shipments = new Shipment();

                $shipments = $shipments->where('client_id', $userClient->id);
                if (isset($request->code) && !empty($request->code)) {
                    $shipments = $shipments->where('code', 'like',  '%' . $request->code . '%');
                }
                if (isset($request->type) && !empty($request->type)) {
                    $shipments = $shipments->where('type', $request->type);
                }

                if (isset($request->is_paid) && !empty($request->is_paid)) {
                    $shipments = $shipments->where('is_withdraw', $request->is_paid);
                }

                if (isset($request->status) && !empty($request->status)) {
                    $shipments = $shipments->where('status_id', $request->status);
                }

                if ($request->has('from') && $request->has('to')) {
                    $shipments->whereBetween('shipping_date', [$request->from, $request->to]);
                }

                if (isset($request->reciver_phone) && !empty($request->reciver_phone)) {
                    $shipments = $shipments->where('reciver_phone',  'like',  '%' . $request->reciver_phone . '%');
                }


                $shipments = $shipments->with(['pay', 'from_address'])->orderBy('client_id')->orderBy('id', 'DESC')->get();
                return response()->json([
                    'status' => 'success',
                    'data' => ShipmentResource::collection($shipments)
                ]);
            } else {
                return response()->json(['message' => 'Not Authorized']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return $e->getMessage();
        }
    }


    public function getShipmentDetailsApi(Request $request)
    {

        $request->validate([
            'shipment_code' => 'required',
        ]);

        try {

            $shipments = new Shipment();

            $shipments = $shipments->where('code', $request->shipment_code);




            $shipments = $shipments->with(['pay', 'from_address'])->orderBy('id', 'DESC')->paginate(20);
            return response()->json([
                'status' => 'success',
                'data' => ShipmentResource::collection($shipments)
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return $e->getMessage();
        }
    }



    public function show($id)
    {
        $shipment = Shipment::find($id);
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.shipments'),
                'path' => fr_route('shipments.index')
            ],
            [
                'name' => __('cargo::view.shipment') . ' | ' . $shipment->code,
            ],
        ]);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.show', compact('shipment'));
    }

    public function edit($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.shipments'),
                'path' => fr_route('shipments.index')
            ],
            [
                'name' => __('cargo::view.edit_shipment'),
            ],
        ]);
        $item = Shipment::findOrFail($id);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.edit')->with(['model' => $item]);
    }

    public function update(Request $request, $id)
    {

        // Normalize phone numbers before validation for updates
        if (isset($request->Shipment['client_phone'])) {
            $request->merge([
                'Shipment' => array_merge($request->Shipment, [
                    'client_phone' => $this->normalizePhoneNumber($request->Shipment['client_phone'])
                ])
            ]);
        }

        if (isset($request->Shipment['reciver_phone'])) {
            $request->merge([
                'Shipment' => array_merge($request->Shipment, [
                    'reciver_phone' => $this->normalizePhoneNumber($request->Shipment['reciver_phone'])
                ])
            ]);
        }

        $request->validate([
            'Shipment.type'            => 'required',
            'Shipment.branch_id'       => 'required',
            'Shipment.shipping_date'   => 'nullable',
            'Shipment.collection_time' => 'nullable',
            'Shipment.client_id'       => 'required',
            'Shipment.client_phone'    => 'nullable|min:5', // Made nullable for updates
            'Shipment.country_code'    => 'nullable',
            'Shipment.client_address'  => 'required',
            'Shipment.reciver_name'    => 'required|string|min:3|max:50',
            'Shipment.reciver_phone'   => 'required|min:7', // Made more flexible
            'Shipment.follow_up_country_code'   => 'nullable',
            'Shipment.reciver_address' => 'required|string|min:8',
            // 'Shipment.from_country_id' => 'required',
            'Shipment.to_country_id'   => 'required',
            //'Shipment.from_state_id'   => 'required',
            'Shipment.to_state_id'     => 'required',
            // 'Shipment.from_area_id'    => 'required',
            'Shipment.to_area_id'      => 'required',
            'Shipment.payment_type'    => 'required',
            'Shipment.payment_method_id' => 'required',
            'Shipment.order_id'          => 'nullable',
            'Shipment.attachments_before_shipping' => 'nullable',
            'Shipment.amount_to_be_collected'      => 'required',
            'Shipment.delivery_time'    => 'nullable',
            'Shipment.total_weight'     => 'required',
            'Shipment.tax'           => 'nullable',
            'Shipment.insurance'     => 'nullable',
            'Shipment.shipping_cost' => 'nullable',
            'Shipment.return_cost'   => 'nullable',
        ]);



        try {
            DB::beginTransaction();
            $model = Shipment::find($id);
            $model->fill($request->Shipment);

            // Delete existing packages first
            foreach (PackageShipment::where('shipment_id', $model->id)->get() as $pack) {
                $pack->delete();
            }

            // Process packages and calculate total weight
            $total_weight = 0;
            $counter = 0;

            if (isset($_POST['Package']) && !empty($_POST['Package'])) {
                if (isset($_POST['Package'][$counter]['package_id'])) {
                    foreach ($_POST['Package'] as $package) {
                        // Save package
                        $package_shipment = new PackageShipment();
                        $package_shipment->fill($package);
                        $package_shipment->shipment_id = $model->id;
                        if (!$package_shipment->save()) {
                            throw new \Exception();
                        }

                        // Add package weight to total
                        $total_weight += isset($package['weight']) ? floatval($package['weight']) : 1;
                    }
                }
            }

            // Update shipment total weight from calculated packages
            if ($total_weight > 0) {
                $model->total_weight = $total_weight;
            }

            // Apply shipping costs using the updated total weight
            $costs = $this->applyShipmentCostNew($model, $_POST['Package'] ?? []);
            $model->fill($costs);

            // Save the final model with all updates
            if (!$model->save()) {
                throw new \Exception();
            }

            event(new UpdateShipment($model));
            DB::commit();

            $model->syncFromMediaLibraryRequest($request->image)->toMediaCollection('attachments');
            return redirect()->route('shipments.show', $model->id)->with(['message_alert' => __('cargo::messages.saved')]);;
        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
            exit;
            return back();
        }
    }

    public function import(Request $request)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.shipments'),
                'path' => fr_route('shipments.index')
            ],
            [
                'name' => __('cargo::view.import_shipments'),
            ],
        ]);
        $shipment = new Shipment;
        $columns = $shipment->getTableColumns();
        $countries = Country::where('covered', 1)->get();
        $states    = State::where('covered', 1)->get();
        $areas     = Area::get();
        $packages  = Package::all();
        $branches  = Branch::where('is_archived', 0)->get();
        $paymentsGateway = BusinessSetting::where("key", "payment_gateway")->where("value", "1")->get();
        $deliveryTimes   = DeliveryTime::all();
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.import')
            ->with(['columns' => $columns, 'countries' => $countries, 'states' => $states, 'areas' => $areas, 'packages' => $packages, 'branches' => $branches, 'deliveryTimes' => $deliveryTimes]);
    }


    public function smartImport(Request $request)
    {
        // under working ...........
        // $request->validate([
        //     'shipments_file' => 'required|file|mimetypes:text/csv,text/plain,application/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet|mimes:xlsx,xls,csv',
        // ]);

        $request->validate([
            'shipments_file' => 'required|file|mimes:xlsx,xls,csv,txt',
        ]);

        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.shipments'),
                'path' => fr_route('shipments.index')
            ],
            [
                'name' => __('cargo::view.import_shipments'),
            ],
        ]);


        $originalName = $request->file('shipments_file')->getClientOriginalName();
        $extension = $request->file('shipments_file')->getClientOriginalExtension(); // Get the correct extension

        // Define storage directory
        $directory = 'excel_filesss';

        // Define a unique filename (to avoid overwriting)
        $filename = time() . '_' . pathinfo($originalName, PATHINFO_FILENAME) . '.' . $extension;

        // Store the file with the correct extension
        $path = $request->file('shipments_file')->storeAs($directory, $filename);

        // Get the full storage path
        $fullPath = storage_path("app/{$path}");


        Session::put('shipments_file', $path);

        // Load the spreadsheet
        $spreadsheet = IOFactory::load($fullPath);
        $worksheet = $spreadsheet->getActiveSheet();

        // Get headers (first row)
        $headers = [];
        foreach ($worksheet->getRowIterator(1, 1) as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);

            foreach ($cellIterator as $cell) {
                $headers[] = $cell->getValue();
            }
        }

        // dd($headers , $path , $fullPath);

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.smart_import')->with(['headers' => $headers, 'path' => $path, 'fullPath' => $fullPath]);


        // Excel::import(new ShipmentsImport, $request->file('shipments_file'));

        // try {
        //     $user_role = auth()->user()->role;
        //     $admin  = 1;
        //     $auth_staff  = 0;
        //     $auth_branch = 3;
        //     $auth_client = 4;


        //     if ($user_role == $auth_client) {
        //         $client = Client::where('user_id', auth()->user()->id)->first();
        //     }

        //     return back()->with(['message_alert' => __('cargo::messages.imported')]);
        // } catch (\Throwable $th) {

        //     return dd($th);
        // }



    }

    public function smartImportFile(Request $request)
    {

        $path =   Session::get('shipments_file');

        $newArray = [];
        foreach ($request->columns as $key => $value) {
            if ($value) {
                $newArray[strtolower($value)] = strtolower($key);
            }
        }


        Excel::import(new ShipmentsImport($newArray, $request), $path);

        session()->forget('errors');
        if (!empty(Session::get('date_error')) && Session::get('date_error') == 1) {
            return redirect()->route('shipments.import');
        } else {
            return redirect()->route('shipments.index')->with(['message_alert' => __('cargo::messages.imported')]);
        }
    }


    public function smartImportAPI(Request $request)
    {

        $request->validate([
            'shipments_file' => 'required|file|mimes:xlsx,xls,csv,txt',
            "columns"        => "required|array",
        ]);


        $originalName = $request->file('shipments_file')->getClientOriginalName();
        $extension = $request->file('shipments_file')->getClientOriginalExtension(); // Get the correct extension

        // Define storage directory
        $directory = 'excel_filesss';

        // Define a unique filename (to avoid overwriting)
        $filename = time() . '_' . pathinfo($originalName, PATHINFO_FILENAME) . '.' . $extension;

        // Store the file with the correct extension
        $path = $request->file('shipments_file')->storeAs($directory, $filename);



        $newArray = [];
        foreach ($request->columns as $key => $value) {
            if ($value) {
                $newArray[strtolower($value)] = strtolower($key);
            }
        }


        Excel::import(new ShipmentsAPIImport($newArray, $request), $path);

        session()->forget('errors');
        if (!empty(Session::get('date_error')) && Session::get('date_error') == 1) {
            return response()->json([
                'success' => false,
                'message' => 'error',
                'errors' => Session::get('errors2'),
                'fails_rows_array_with_errors' => Session::get('fails_rows_array_with_errors')
            ]);
        } else {
            return response()->json([
                'success' => true,
                'message' => 'success',
            ]);
        }
    }

    // public function parseImport(Request $request)
    // {

    //     $request->validate([
    //         'shipments_file' => 'required|mimes:csv,txt',
    //         "columns"        => "required|array|min:5",
    //     ]);



    //     $path = $request->file('shipments_file')->getRealPath();
    //     $data = [];
    //     $csv = new CSVUtility("testfile");
    //     $csv->readCSV($path);
    //     $totalRows = $csv->totalRows();

    //     for ($row = 0; $row < $totalRows; $row++) {

    //         $value = $csv->getRow($row);
    //         array_push($data, $value);
    //     }





    //     // if (count($data[0]) != count($request->columns)) {
    //     //     return back()->with(['error_message_alert' => __('cargo::view.this_file_you_are_trying_to_import_is_not_the_file_that_you_should_upload')]);
    //     // }

    //     // if (!in_array('type', $request->columns) || !in_array('client_phone', $request->columns) || !in_array('client_address', $request->columns) || !in_array('branch_id', $request->columns) || !in_array('shipping_date', $request->columns) || !in_array('reciver_name', $request->columns) || !in_array('reciver_phone', $request->columns) || !in_array('reciver_address', $request->columns) || !in_array('from_country_id', $request->columns) || !in_array('to_country_id', $request->columns) || !in_array('from_state_id', $request->columns) || !in_array('to_state_id', $request->columns) || !in_array('to_area_id', $request->columns) || !in_array('from_area_id', $request->columns) || !in_array('payment_type', $request->columns) || !in_array('payment_method_id', $request->columns) || !in_array('package_id', $request->columns)) {
    //     //     return back()->with(['error_message_alert' => __('cargo::view.make_sure_all_required_parameters_in_CSV')]);
    //     // }
    //     // if (auth()->user()->can('import-shipments')) {
    //     //     if (!in_array('client_id', $request->columns)) {
    //     //         return back()->with(['error_message_alert' => __('cargo::view.make_sure_all_required_parameters_in_CSV')]);
    //     //     }
    //     // }



    //   //  try {
    //         $user_role = auth()->user()->role;
    //         $admin  = 1;
    //         $auth_staff  = 0;
    //         $auth_branch = 3;
    //         $auth_client = 4;

    //         unset($data[0]);

    //         if ($user_role == $auth_client) {
    //             $client = Client::where('user_id', auth()->user()->id)->first();
    //         }

    //         foreach ($data as $row) {
    //             $area = null;
    //             for ($i = 0; $i < 7; $i++) {

    //                 if ($user_role != $auth_client) {
    //                     if ($request->columns[$i] == 'client_id') {
    //                         if (!Client::find($row[$i])) {
    //                             return back()->with(['error_message_alert' => __('cargo::view.invalid_client')]);
    //                         }
    //                         $client = Client::where('id', $row[$i])->first();
    //                     }
    //                 }





    //                 $new_shipment['branch_id'] = Branch::first()?->id ?? 0;

    //                 $new_shipment['client_address'] = ClientAddress::where('client_id', $client->id)->first()?->id  ?? 0;





    //                 if ( $request->columns[$i] == 'to_area_id') {



    //                    $area = Area::where('name' , 'like' , '%' . $row[$i] . '%')->first();
    //                     if ( ! $area ) {
    //                         return back()->with(['error_message_alert' => __('cargo::view.invalid_area')]);
    //                     }
    //                     $new_shipment['from_area_id']  = $client->area_id;
    //                     $new_shipment['from_state_id'] = $client->state_id;



    //                     $new_shipment['from_country_id'] = 65;
    //                     $new_shipment['to_area_id']    =  $area ? $area->id : 0;
    //                     $new_shipment['to_state_id']   = $area ? $area->state_id : 0;
    //                     $new_shipment['to_country_id'] = 65;



    //                 }

    //                 if ( $request->columns[$i] == 'open_shipment') {

    //                     if ( $row[$i] == 'yes' || $row[$i] == 'YES' || $row[$i] == 'Yes') {
    //                         $new_shipment['open_shipment'] = 'yes';
    //                     } else {
    //                         $new_shipment['open_shipment'] = 'no';
    //                     }
    //                 }




    //                 $new_shipment['client_phone'] = $client->responsible_mobile;
    //                 $new_shipment['client_id'] = $client->id;
    //                 $new_shipment['payment_type']  =  2 ;


    //                 // End Validation

    //                 if ($request->columns[$i] != 'package_id' && $request->columns[$i] != 'description' && $request->columns[$i] != 'height' && $request->columns[$i] != 'width' && $request->columns[$i] != 'length' && $request->columns[$i] != 'weight' && $request->columns[$i] != 'qty') {

    //                     if ($request->columns[$i] == 'amount_to_be_collected') {

    //                         if ($row[$i] == "" || $row[$i] == " " || !is_numeric($row[$i])) {
    //                             $new_shipment[$request->columns[$i]] = 0;
    //                         } else {
    //                             $new_shipment[$request->columns[$i]] = $row[$i];
    //                         }
    //                     } elseif ($request->columns[$i] == 'client_phone') {
    //                         if ($row[$i] == "" || $row[$i] == " ") {
    //                             $new_shipment[$request->columns[$i]] = $client->responsible_mobile ?? $auth_user->phone;
    //                         } else {
    //                             $new_shipment[$request->columns[$i]] = $row[$i];
    //                         }
    //                     } else {
    //                         $new_shipment[$request->columns[$i]] = $row[$i];
    //                     }
    //                 } else {
    //                     if ($request->columns[$i] == 'package_id') {
    //                         $new_package[$request->columns[$i]] = intval($row[$i]);
    //                     } else {
    //                         if ($request->columns[$i] != 'description') {
    //                             if ($row[$i] == "" || $row[$i] == " " || !is_numeric($row[$i])) {
    //                                 $new_package[$request->columns[$i]] = 1;

    //                                 if ($request->columns[$i] == 'weight') {
    //                                     $new_shipment['total_weight'] = 1;
    //                                 }
    //                             } else {
    //                                 $new_package[$request->columns[$i]] = $row[$i];
    //                                 if ($request->columns[$i] == 'weight') {
    //                                     $new_shipment['total_weight'] = $row[$i];
    //                                 }
    //                             }
    //                         } else {
    //                             $new_package[$request->columns[$i]] = $row[$i];
    //                         }
    //                     }
    //                 }

    //                 if ($request->columns[$i] == 'delivery_time') {
    //                     if (isset($row[$i]) && !empty($row[$i])) {
    //                         if (!DeliveryTime::find($row[$i])) {
    //                             return back()->with(['error_message_alert' => __('cargo::view.invalid_delivery_time')]);
    //                         }
    //                     }
    //                 }
    //             }

    //             $new_shipment['payment_method_id'] = 'cash_payment';
    //             $new_shipment['to_area_id']    =  $area ? $area->id : 0;

    //             $request['Shipment'] = $new_shipment;




    //           //  $new_shipment['package_id'] = Package::first()?->id;

    //              $packages[0] = Package::first()?->id ;
    //              $request['Package'] = $packages;




    //             $this->storeShipment($request);
    //         }

    //         return back()->with(['message_alert' => __('cargo::messages.imported')]);
    //     // } catch (\Throwable $th) {

    //     //     return dd($th);
    //     // }
    // }



    public function parseImport(Request $request)
    {
        $request->validate([
            'shipments_file' => 'required|mimes:xlsx,xls',
            "columns"        => "required|array|min:5",
        ]);

        $file = $request->file('shipments_file');

        $data = Excel::toArray([], $file)[0]; // Get the first sheet as an array

        $user_role = auth()->user()->role;
        $auth_client = 4;
        unset($data[0]); // remove the header row

        if ($user_role == $auth_client) {
            $client = Client::where('user_id', auth()->id())->first();
        }

        foreach ($data as $row) {
            $area = null;
            $new_shipment = [];
            $new_package = [];

            if (empty(trim($row[0])) && empty(trim($row[1]))) {
                continue;
            }
            //dd($request->columns);
            for ($i = 0; $i < 10; $i++) {
                if ($user_role != $auth_client && $request->columns[$i] === 'client_id') {
                    if (!Client::find($row[$i])) {
                        return back()->with(['error_message_alert' => __('cargo::view.invalid_client')]);
                    }
                    $client = Client::find($row[$i]);
                }

                $new_shipment['branch_id'] = Branch::first()?->id ?? 0;
                $new_shipment['client_address'] = ClientAddress::where('client_id', $client->id)->first()?->id ?? 0;

                if ($request->columns[$i] === 'to_area_id') {
                    // Handle to_area_id - search by ID first, then by name (supporting JSON names)
                    $area_input = $row[$i];
                    $area = Area::findByIdOrName($area_input);

                    if (!$area) {
                        return back()->with(['error_message_alert' => __('cargo::view.invalid_area') . ': ' . $area_input]);
                    }

                    $new_shipment['from_area_id'] = $client->area_id;
                    $new_shipment['from_state_id'] = $client->state_id;
                    $new_shipment['from_country_id'] = 65;
                    $new_shipment['to_area_id'] = $area->id;
                    $new_shipment['to_state_id'] = $area->state_id;
                    $new_shipment['to_country_id'] = 65;
                }

                // Handle to_state_id from Excel import
                if ($request->columns[$i] === 'to_state_id' && !empty($row[$i]) && is_numeric($row[$i])) {
                    $state = State::where('covered', 1)->find($row[$i]);
                    if ($state) {
                        $new_shipment['to_state_id'] = $state->id;
                        // If area was already set, verify it belongs to this state
                        if (isset($new_shipment['to_area_id']) && $area && $area->state_id != $state->id) {
                            // Area doesn't belong to specified state - set area to null to force selection
                            $new_shipment['to_area_id'] = null;
                        }
                    }
                }

                if ($request->columns[$i] == 'open_shipment') {
                    $new_shipment['open_shipment'] = strtolower($row[$i]);
                }


                if ($request->columns[$i] == 'order_id' && !empty($row[$i])) {

                    $new_shipment['order_id'] = strtolower($row[$i]);
                }

                if ($request->columns[$i] == 'comment' && !empty($row[$i])) {
                    $new_shipment['comment'] = strtolower($row[$i]);
                }

                if ($request->columns[$i] == 'reciver_name') {
                    $new_shipment['reciver_name'] = strtolower($row[$i]);
                }
                if ($request->columns[$i] == 'reciver_address') {
                    $new_shipment['reciver_address'] = strtolower($row[$i]);
                }
                if ($request->columns[$i] == 'reciver_phone') {
                    $new_shipment['reciver_phone'] = strtolower($row[$i]);
                }


                $new_shipment['client_phone'] = $client->responsible_mobile;
                $new_shipment['client_id'] = $client->id;
                $new_shipment['payment_type'] = 2;

                if (!in_array($request->columns[$i], ['package_id', 'description', 'height', 'width', 'length', 'weight', 'qty'])) {
                    if ($request->columns[$i] === 'amount_to_be_collected') {
                        $new_shipment[$request->columns[$i]] = is_numeric($row[$i]) ? $row[$i] : 0;
                    } elseif ($request->columns[$i] === 'client_phone') {
                        $new_shipment[$request->columns[$i]] = $row[$i] ?: ($client->responsible_mobile ?? auth()->user()->phone);
                    } else {
                        $new_shipment[$request->columns[$i]] = $row[$i];
                    }
                } else {
                    if ($request->columns[$i] === 'package_id') {
                        $new_package[$request->columns[$i]] = intval($row[$i]);
                    } else {
                        if ($request->columns[$i] !== 'description') {
                            $new_package[$request->columns[$i]] = is_numeric($row[$i]) ? $row[$i] : 1;
                            if ($request->columns[$i] === 'weight') {
                                $new_shipment['total_weight'] = $new_package[$request->columns[$i]];
                            }
                        } else {
                            //  $new_package[$request->columns[$i]] = $row[$i];
                        }
                    }
                }

                if ($request->columns[$i] === 'delivery_time' && !empty($row[$i])) {
                    if (!DeliveryTime::find($row[$i])) {
                        return back()->with(['error_message_alert' => __('cargo::view.invalid_delivery_time')]);
                    }
                }

                if ($request->columns[$i] == 'type') {

                    if (strtolower($row[$i]) == 'delivery') {
                        $new_shipment['type'] = 1;
                    } else {
                        $new_shipment['type'] = 3;
                    }
                }

                // Handle from_state_id from Excel import
                if ($request->columns[$i] == 'from_state_id' && !empty($row[$i])) {
                    $state = State::find($row[$i]);
                    if ($state) {
                        $new_shipment['from_state_id'] = $state->id;
                    }
                }
            }

            $new_shipment['payment_method_id'] = 'cash_payment';
            $new_shipment['to_area_id'] = $area->id ?? 0;

            $request['Shipment'] = $new_shipment;

            // Fix: Properly structure package data for storeShipment method
            $default_package = Package::first();
            $request['Package'] = [
                [
                    'package_id' => $default_package?->id ?? 1,
                    'qty' => 1,
                    'weight' => 1,
                    'length' => 1,
                    'width' => 1,
                    'height' => 1
                ]
            ];

            //  dd($new_shipment['type']);
            $this->storeShipment($request);
        }

        return back()->with(['message_alert' => __('cargo::messages.imported')]);
    }



    // public function parseImportAPI(Request $request)
    // {

    //     $request->validate([
    //         'shipments_file' => 'required|mimes:csv,txt',
    //         "columns"        => "required|array|min:5",
    //     ]);

    //     $path = $request->file('shipments_file')->getRealPath();
    //     $data = [];
    //     $csv = new CSVUtility("testfile");
    //     $csv->readCSV($path);
    //     $totalRows = $csv->totalRows();

    //     for ($row = 0; $row < $totalRows; $row++) {

    //         $value = $csv->getRow($row);
    //         array_push($data, $value);
    //     }




    //     try {

    //         $apihelper = new ApiHelper();
    //         $user = $apihelper->checkUser($request);
    //         if (empty($user)) {
    //             return response()->json(['success' => false, 'message' => 'Not Authorized']);
    //         }


    //         $user_role = $user->role;
    //         $admin  = 1;
    //         $auth_staff  = 0;
    //         $auth_branch = 3;
    //         $auth_client = 4;

    //         unset($data[0]);

    //         if ($user_role == $auth_client) {
    //             $client = Client::where('user_id', $user->id)->first();
    //         }

    //         foreach ($data as $row) {
    //             $area = null;
    //             for ($i = 0; $i < count($row); $i++) {

    //                 if ($user_role != $auth_client) {
    //                     if ($request->columns[$i] == 'client_id') {
    //                         if (!Client::find($row[$i])) {
    //                             return back()->with(['error_message_alert' => __('cargo::view.invalid_client')]);
    //                         }
    //                         $client = Client::where('id', $row[$i])->first();
    //                     }
    //                 }





    //                 if ($request->columns[$i] == 'to_area_id') {


    //                     $area = Area::where('name', 'like', '%' . $row[$i] . '%')->first();
    //                     if (! $area) {
    //                         return back()->with(['error_message_alert' => __('cargo::view.invalid_area')]);
    //                     }
    //                     $new_shipment['from_area_id']  = $client->area_id;
    //                     $new_shipment['from_state_id'] = $client->state_id;



    //                     $new_shipment['from_country_id'] = 65;
    //                     $new_shipment['to_area_id']    =  $area ? $area->id : 0;
    //                     $new_shipment['to_state_id']   = $area ? $area->state_id : 0;
    //                     $new_shipment['to_country_id'] = 65;
    //                 }

    //                 if ($request->columns[$i] == 'open_shipment') {

    //                     if ($row[$i] == 'yes' || $row[$i] == 'YES' || $row[$i] == 'Yes') {
    //                         $new_shipment['open_shipment'] = 'yes';
    //                     } else {
    //                         $new_shipment['open_shipment'] = 'no';
    //                     }
    //                 }


    //                 // if ($request->columns[$i] == 'payment_method_id') {
    //                 //     $paymentSettings = resolve(\Modules\Payments\Entities\PaymentSetting::class)->toArray();
    //                 //     if (!isset($paymentSettings[$row[$i]])) {
    //                 //         return back()->with(['error_message_alert' => __('cargo::view.invalid_payment_method')]);
    //                 //     }
    //                 // }



    //                 $new_shipment['branch_id'] = Branch::first()?->id ?? 0;

    //                 $new_shipment['client_address'] = ClientAddress::where('client_id', $client->id)->first()?->id  ?? 0;




    //                 $new_shipment['client_phone'] = $client->responsible_mobile;
    //                 $new_shipment['client_id'] = $client->id;
    //                 $new_shipment['payment_type']  =  2;


    //                 // End Validation

    //                 if ($request->columns[$i] != 'package_id' && $request->columns[$i] != 'description' && $request->columns[$i] != 'height' && $request->columns[$i] != 'width' && $request->columns[$i] != 'length' && $request->columns[$i] != 'weight' && $request->columns[$i] != 'qty') {

    //                     if ($request->columns[$i] == 'amount_to_be_collected') {

    //                         if ($row[$i] == "" || $row[$i] == " " || !is_numeric($row[$i])) {
    //                             $new_shipment[$request->columns[$i]] = 0;
    //                         } else {
    //                             $new_shipment[$request->columns[$i]] = $row[$i];
    //                         }
    //                     } elseif ($request->columns[$i] == 'client_phone') {
    //                         if ($row[$i] == "" || $row[$i] == " ") {
    //                             $new_shipment[$request->columns[$i]] = $client->responsible_mobile ?? $auth_user->phone;
    //                         } else {
    //                             $new_shipment[$request->columns[$i]] = $row[$i];
    //                         }
    //                     } else {
    //                         $new_shipment[$request->columns[$i]] = $row[$i];
    //                     }
    //                 } else {
    //                     if ($request->columns[$i] == 'package_id') {
    //                         $new_package[$request->columns[$i]] = intval($row[$i]);
    //                     } else {
    //                         if ($request->columns[$i] != 'description') {
    //                             if ($row[$i] == "" || $row[$i] == " " || !is_numeric($row[$i])) {
    //                                 $new_package[$request->columns[$i]] = 1;

    //                                 if ($request->columns[$i] == 'weight') {
    //                                     $new_shipment['total_weight'] = 1;
    //                                 }
    //                             } else {
    //                                 $new_package[$request->columns[$i]] = $row[$i];
    //                                 if ($request->columns[$i] == 'weight') {
    //                                     $new_shipment['total_weight'] = $row[$i];
    //                                 }
    //                             }
    //                         } else {
    //                             $new_package[$request->columns[$i]] = $row[$i];
    //                         }
    //                     }
    //                 }

    //                 if ($request->columns[$i] == 'delivery_time') {
    //                     if (isset($row[$i]) && !empty($row[$i])) {
    //                         if (!DeliveryTime::find($row[$i])) {
    //                             return response()->json(['success' => false, 'message' => ' Invalid Delivery Time']);
    //                         }
    //                     }
    //                 }
    //             }

    //             $new_shipment['payment_method_id'] = 'cash_payment';

    //             $new_shipment['from_country_id'] = 65;
    //             $new_shipment['to_country_id'] = 65;

    //             $new_shipment['to_area_id']    =  $area ? $area->id : 0;

    //             $request['Shipment'] = $new_shipment;



    //             $packages[0] = Package::first()?->id;
    //             $request['Package'] = $packages;


    //             $this->storeShipment($request);
    //         }

    //         return response()->json(['success' => true, 'message' => ' Imported']);
    //     } catch (\Throwable $th) {

    //         return response()->json(['success' => false, 'message' => ' Error ' . $th->getMessage()]);
    //     }
    // }

    public function parseImportAPI(Request $request)
    {
        $request->validate([
            'shipments_file' => 'required|mimes:xlsx,xls',
            'columns' => 'required|array|min:5',
        ]);

        try {
            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if (empty($user)) {
                return response()->json(['success' => false, 'message' => 'Not Authorized']);
            }

            $data = Excel::toCollection(null, $request->file('shipments_file'))->first();
            if ($data->isEmpty()) {
                return response()->json(['success' => false, 'message' => 'Empty File']);
            }

            // Skip header
            $data = $data->slice(1);

            $user_role = $user->role;
            $auth_client = 4;
            $client = null;

            if ($user_role == $auth_client) {
                $client = Client::where('user_id', $user->id)->first();
            }

            foreach ($data as $row) {
                $row = $row->toArray();
                $area = null;
                $new_shipment = [];
                $new_package = [];

                for ($i = 0; $i < count($request->columns); $i++) {
                    $column = $request->columns[$i];
                    $value = $row[$i] ?? null;

                    if ($user_role != $auth_client && $column == 'client_id') {
                        if (!Client::find($value)) {
                            return back()->with(['error_message_alert' => __('cargo::view.invalid_client')]);
                        }
                        $client = Client::where('id', $value)->first();
                    }

                    if ($column == 'to_area_id') {
                        // Handle to_area_id - search by ID first, then by name (supporting JSON names)
                        $area_input = $value;
                        $area = Area::findByIdOrName($area_input);

                        if (!$area) {
                            return back()->with(['error_message_alert' => __('cargo::view.invalid_area') . ': ' . $area_input]);
                        }

                        $new_shipment['from_area_id'] = $client->area_id;
                        $new_shipment['from_state_id'] = $client->state_id;
                        $new_shipment['from_country_id'] = 65;
                        $new_shipment['to_area_id'] = $area->id;
                        $new_shipment['to_state_id'] = $area->state_id;
                        $new_shipment['to_country_id'] = 65;
                    }

                    // Handle to_state_id from Excel import
                    if ($column == 'to_state_id' && !empty($value) && is_numeric($value)) {
                        $state = State::where('covered', 1)->find($value);
                        if ($state) {
                            $new_shipment['to_state_id'] = $state->id;
                            // If area was already set, verify it belongs to this state
                            if (isset($new_shipment['to_area_id']) && isset($area) && $area && $area->state_id != $state->id) {
                                // Area doesn't belong to specified state - set area to null to force selection
                                $new_shipment['to_area_id'] = null;
                            }
                        }
                    }

                    if ($column == 'open_shipment') {
                        $new_shipment['open_shipment'] = in_array(strtolower($value), ['yes']) ? 'yes' : 'no';
                    }

                    if ($request->columns[$i] == 'order_id' && !empty($row[$i])) {
                        $new_shipment['order_id'] = strtolower($row[$i]);
                    }

                    if ($request->columns[$i] == 'comment' && !empty($row[$i])) {
                        $new_shipment['comment'] = strtolower($row[$i]);
                    }


                    if ($column == 'reciver_name') {
                        $new_shipment['reciver_name'] = strtolower($row[$i]);
                    }
                    if ($column == 'reciver_address') {
                        $new_shipment['reciver_address'] = strtolower($row[$i]);
                    }
                    if ($column == 'reciver_phone') {
                        $new_shipment['reciver_phone'] = strtolower($row[$i]);
                    }


                    $new_shipment['branch_id'] = Branch::first()?->id ?? 0;
                    $new_shipment['client_address'] = ClientAddress::where('client_id', $client->id)->first()?->id ?? 0;
                    $new_shipment['client_phone'] = $client->responsible_mobile;
                    $new_shipment['client_id'] = $client->id;
                    $new_shipment['payment_type'] = 2;

                    if (!in_array($column, ['package_id', 'description', 'height', 'width', 'length', 'weight', 'qty'])) {
                        if ($column == 'amount_to_be_collected') {
                            $new_shipment[$column] = is_numeric($value) ? $value : 0;
                        } elseif ($column == 'client_phone') {
                            $new_shipment[$column] = $value ?: ($client->responsible_mobile ?? $user->phone);
                        } else {
                            $new_shipment[$column] = $value;
                        }
                    } else {
                        if ($column == 'package_id') {
                            $new_package[$column] = intval($value);
                        } else {
                            $new_package[$column] = (is_numeric($value) && $value > 0) ? $value : 1;
                            if ($column == 'weight') {
                                $new_shipment['total_weight'] = $new_package[$column];
                            }
                        }
                    }

                    if ($column == 'delivery_time' && !empty($value)) {
                        if (!DeliveryTime::find($value)) {
                            return response()->json(['success' => false, 'message' => 'Invalid Delivery Time']);
                        }
                    }
                    if ($request->columns[$i] == 'type') {

                        if (strtolower($row[$i]) == 'delivery') {
                            $new_shipment['type'] = 1;
                        } else {
                            $new_shipment['type'] = 3;
                        }
                    }
                }

                $new_shipment['payment_method_id'] = 'cash_payment';
                $new_shipment['from_country_id'] = 65;
                $new_shipment['to_country_id'] = 65;
                $new_shipment['to_area_id'] = $area ? $area->id : 0;

                $request['Shipment'] = $new_shipment;

                // Fix: Properly structure package data - ensure it has package_id key
                if (isset($new_package['package_id'])) {
                    // If package_id is already set, use the existing structure
                    $request['Package'] = [$new_package];
                } else {
                    // If not, create proper package structure
                    $default_package = Package::first();
                    $request['Package'] = [
                        [
                            'package_id' => $default_package?->id ?? 1,
                            'qty' => 1,
                            'weight' => 1,
                            'length' => 1,
                            'width' => 1,
                            'height' => 1
                        ]
                    ];
                }

                $this->storeShipment($request);
            }

            return response()->json(['success' => true, 'message' => 'Imported Successfully']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => 'Error: ' . $th->getMessage()]);
        }
    }


    public function change(Request $request, $to)
    {
        if (isset($request->ids)) {
            $action = new StatusManagerHelper();
            $response = $action->change_shipment_status($request->ids, $to);
            if ($response['success']) {
                event(new ShipmentAction($to, $request->ids));
                return back()->with(['message_alert' => __('cargo::messages.saved')]);
            }
        } else {
            return back()->with(['message_alert' => __('cargo::messages.select_error')]);
        }
    }

    public function createPickupMission(Request $request, $type)
    {

        // try {

        if (!is_array($request->checked_ids)) {
            $request->checked_ids = json_decode($request->checked_ids, true);
        }
        foreach ($request->checked_ids as $shipment_id) {


            DB::beginTransaction();
            $model = new Mission();
            $model->fill($request['Mission']);
            $model->status_id = Mission::REQUESTED_STATUS;




            $model->type = Mission::PICKUP_TYPE;
            if (!$model->save()) {
                throw new \Exception();
            }

            $code = '';
            for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
                $code .= '0';
            }
            $code   =   substr($code, 0, -strlen($model->id));
            $model->code = $code . $model->id;
            $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;

            if (!$model->save()) {
                throw new \Exception();
            }

            //change shipment status to requested
            $action = new StatusManagerHelper();

            $response = $action->change_shipment_status([$shipment_id], Shipment::REQUESTED_STATUS, $model->id);

            //Calaculate Amount
            $helper = new TransactionHelper();
            $helper->calculate_mission_amount($model->id);

            if ($model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::PICKUP_TYPE) == 0) {
                $shipment = Shipment::find($shipment_id);
                $shipment_mission = new ShipmentMission();
                $shipment_mission->shipment_id = $shipment->id;
                $shipment_mission->mission_id = $model->id;
                if ($shipment_mission->save()) {
                    $shipment->mission_id = $model->id;
                    $shipment->save();
                }
            }

            event(new ShipmentAction(Shipment::REQUESTED_STATUS, [$shipment_id]));

            event(new CreateMission($model));

            $shipment_ids = ShipmentMission::where('mission_id', $model->id)->pluck('shipment_id');
            $shipment_data = Shipment::whereIn('id', $shipment_ids)->get();

            foreach ($shipment_data as $shipment) {
                if ($model->type == Mission::SUPPLY_TYPE  || $model->type == __('cargo::view.supply') || $model->type == __('cargo::view.delivery') || $model->type == Mission::DELIVERY_TYPE) {

                    $address  =   json_decode($shipment->to_area?->name, true)[app()->getLocale()] .  $shipment->reciver_address;

                    Mission::where('id', $model->id)->update([
                        'address' => $address,
                        'area' => $shipment->to_area_id,
                        'client_id' => $shipment->client_id,
                        'created_by' => auth()->user()->id,
                    ]);
                } else {
                    $address  =   json_decode($shipment->from_area?->name, true)[app()->getLocale()] .  $shipment->client->addressess()->first()?->address;
                    Mission::where('id', $model->id)->update([
                        'address' => $address,
                        'area' => $shipment->from_area_id,
                        'client_id' => $shipment->client_id,
                        'created_by' => auth()->user()->id,

                    ]);
                }
            }

            DB::commit();
        }

        if ($request->is('api/*')) {
            return $model;
        } else {
            return back()->with(['message_alert' => __('cargo::messages.created')]);
        }
        // } catch (\Exception $e) {
        //     DB::rollback();

        //     return back();
        // }

        // try {

        //     if(!is_array($request->checked_ids)){
        //         $request->checked_ids = json_decode($request->checked_ids, true);
        //     }

        //     DB::beginTransaction();
        //     $model = new Mission();
        //     $model->fill($request['Mission']);
        //     $model->status_id = Mission::REQUESTED_STATUS;
        //     $model->type = Mission::PICKUP_TYPE;
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }

        //     $code = '';
        //     for($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++){
        //         $code .= '0';
        //     }
        //     $code   =   substr($code, 0, -strlen($model->id));
        //     $model->code = $code.$model->id;
        //     $model->code = ShipmentSetting::getVal('mission_prefix').$code.$model->id;

        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }

        //     //change shipment status to requested
        //     $action = new StatusManagerHelper();

        //     $response = $action->change_shipment_status($request->checked_ids, Shipment::REQUESTED_STATUS, $model->id);

        //     //Calaculate Amount
        //     $helper = new TransactionHelper();
        //     $helper->calculate_mission_amount($model->id);

        //     foreach ($request->checked_ids as $shipment_id) {
        //         if ($model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::PICKUP_TYPE) == 0)
        //         {
        //             $shipment = Shipment::find($shipment_id);
        //             $shipment_mission = new ShipmentMission();
        //             $shipment_mission->shipment_id = $shipment->id;
        //             $shipment_mission->mission_id = $model->id;
        //             if ($shipment_mission->save()) {
        //                 $shipment->mission_id = $model->id;
        //                 $shipment->save();
        //             }
        //         }
        //     }

        //     event(new ShipmentAction( Shipment::REQUESTED_STATUS,$request->checked_ids));

        //     event(new CreateMission($model));

        //     DB::commit();


        //     if($request->is('api/*')){
        //          return $model;
        //     }else{
        //         return back()->with(['message_alert' => __('cargo::messages.created')]);
        //     }

        // } catch (\Exception $e) {
        //     DB::rollback();
        //     print_r($e->getMessage());
        //     exit;

        //     flash(translate("Error"))->error();
        //     return back();
        // }
    }

    public function createDeliveryMission(Request $request, $type)
    {
        try {
            $request->checked_ids = json_decode($request->checked_ids, true);

            foreach ($request->checked_ids as $shipment_id) {


                $shipment_data = Shipment::find($shipment_id);
                DB::beginTransaction();
                $model = new Mission();
                // $model->fill($request['Mission']);
                $model->code = -1;
                $model->status_id = Mission::REQUESTED_STATUS;
                $model->type = Mission::DELIVERY_TYPE;



                $model->otp  = MissionPRNG::get();
                // if(ShipmentSetting::getVal('def_shipment_conf_type')=='otp'){
                //     $model->otp = MissionPRNG::get();
                // }
                if (!$model->save()) {
                    throw new \Exception();
                }
                $code = '';
                for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
                    $code .= '0';
                }
                $code   =   substr($code, 0, -strlen($model->id));
                $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;
                if (!$model->save()) {
                    throw new \Exception();
                }


                if (true) {
                    // $model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::DELIVERY_TYPE) == 0
                    $shipment = Shipment::find($shipment_id);
                    $shipment_mission = new ShipmentMission();
                    $shipment_mission->shipment_id = $shipment->id;
                    $shipment_mission->mission_id = $model->id;
                    if ($shipment_mission->save()) {
                        $shipment->mission_id = $model->id;
                        $shipment->save();
                    }
                }
                //Calaculate Amount
                $helper = new TransactionHelper();
                $helper->calculate_mission_amount($model->id);

                event(new CreateMission($model));

                $shipment_ids = ShipmentMission::where('mission_id', $model->id)->pluck('shipment_id');
                $shipment_data = Shipment::whereIn('id', $shipment_ids)->get();

                foreach ($shipment_data as $shipment) {
                    if ($model->type == Mission::SUPPLY_TYPE  || $model->type == __('cargo::view.supply') || $model->type == __('cargo::view.delivery') || $model->type == Mission::DELIVERY_TYPE) {

                        $address  =   json_decode($shipment->to_area?->name, true)[app()->getLocale()] .  $shipment->reciver_address;

                        Mission::where('id', $model->id)->update([
                            'address' => $address,
                            'area' => $shipment->to_area_id,
                            'client_id' => $shipment->client_id,
                            'created_by' => auth()->user()->id,
                        ]);
                    } else {
                        $address  =   json_decode($shipment->from_area?->name, true)[app()->getLocale()] .  $shipment->client->addressess()->first()?->address;
                        Mission::where('id', $model->id)->update([
                            'address' => $address,
                            'area' => $shipment->from_area_id,
                            'client_id' => $shipment->client_id,
                            'created_by' => auth()->user()->id,

                        ]);
                    }
                }

                DB::commit();
            }


            if ($request->is('api/*')) {
                return $model;
            } else {
                return back()->with(['message_alert' => __('cargo::messages.created')]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
            exit;

            flash(translate("Error"))->error();
            return back();
        }

        // try {
        //     $request->checked_ids = json_decode($request->checked_ids, true);
        //     DB::beginTransaction();
        //     $model = new Mission();
        //     // $model->fill($request['Mission']);
        //     $model->code = -1;
        //     $model->status_id = Mission::REQUESTED_STATUS;
        //     $model->type = Mission::DELIVERY_TYPE;
        //     $model->otp  = MissionPRNG::get();
        //     // if(ShipmentSetting::getVal('def_shipment_conf_type')=='otp'){
        //     //     $model->otp = MissionPRNG::get();
        //     // }
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }
        //     $code = '';
        //     for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
        //         $code .= '0';
        //     }
        //     $code   =   substr($code, 0, -strlen($model->id));
        //     $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }
        //     foreach ($request->checked_ids as $shipment_id) {


        //         if ($model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::DELIVERY_TYPE) == 0) {
        //             $shipment = Shipment::find($shipment_id);
        //             $shipment_mission = new ShipmentMission();
        //             $shipment_mission->shipment_id = $shipment->id;
        //             $shipment_mission->mission_id = $model->id;
        //             if ($shipment_mission->save()) {
        //                 $shipment->mission_id = $model->id;
        //                 $shipment->save();
        //             }
        //         }
        //     }
        //     //Calaculate Amount
        //     $helper = new TransactionHelper();
        //     $helper->calculate_mission_amount($model->id);

        //     event(new CreateMission($model));
        //     DB::commit();

        //     if ($request->is('api/*')) {
        //         return $model;
        //     } else {
        //         return back()->with(['message_alert' => __('cargo::messages.created')]);
        //     }
        // } catch (\Exception $e) {
        //     DB::rollback();
        //     print_r($e->getMessage());
        //     exit;

        //     flash(translate("Error"))->error();
        //     return back();
        // }
    }

    public function createTransferMission(Request $request, $type)
    {
        try {
            $request->checked_ids = json_decode($request->checked_ids, true);
            foreach ($request->checked_ids as $shipment_id) {

                DB::beginTransaction();
                $model = new Mission();
                $model->fill($request['Mission']);
                $model->code = -1;
                $model->status_id = Mission::REQUESTED_STATUS;
                $model->type = Mission::TRANSFER_TYPE;
                if (!$model->save()) {
                    throw new \Exception();
                }
                $code = '';
                for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
                    $code .= '0';
                }
                $code   =   substr($code, 0, -strlen($model->id));
                $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;
                if (!$model->save()) {
                    throw new \Exception();
                }
                // if ($model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::TRANSFER_TYPE) == 0) {
                $shipment = Shipment::find($shipment_id);
                $shipment_mission = new ShipmentMission();
                $shipment_mission->shipment_id = $shipment->id;
                $shipment_mission->mission_id = $model->id;
                if ($shipment_mission->save()) {
                    $shipment->mission_id = $model->id;
                    $shipment->save();
                }
                // }

                //Calaculate Amount
                $helper = new TransactionHelper();
                $helper->calculate_mission_amount($model->id);


                event(new CreateMission($model));

                $shipment_ids = ShipmentMission::where('mission_id', $model->id)->pluck('shipment_id');
                $shipment_data = Shipment::whereIn('id', $shipment_ids)->get();

                foreach ($shipment_data as $shipment) {
                    if ($model->type == Mission::SUPPLY_TYPE  || $model->type == __('cargo::view.supply') || $model->type == __('cargo::view.delivery') || $model->type == Mission::DELIVERY_TYPE) {

                        $address  =   json_decode($shipment->to_area?->name, true)[app()->getLocale()] .  $shipment->reciver_address;

                        Mission::where('id', $model->id)->update([
                            'address' => $address,
                            'area' => $shipment->to_area_id,
                            'client_id' => $shipment->client_id,
                            'created_by' => auth()->user()->id,
                        ]);
                    } else {
                        $address  =   json_decode($shipment->from_area?->name, true)[app()->getLocale()] .  $shipment->client->addressess()->first()?->address;
                        Mission::where('id', $model->id)->update([
                            'address' => $address,
                            'area' => $shipment->from_area_id,
                            'client_id' => $shipment->client_id,
                            'created_by' => auth()->user()->id,

                        ]);
                    }
                }
                DB::commit();
            }


            if ($request->is('api/*')) {
                return $model;
            } else {
                return back()->with(['message_alert' => __('cargo::messages.created')]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
            exit;

            flash(translate("Error"))->error();
            return back();
        }

        // try {
        //     $request->checked_ids = json_decode($request->checked_ids, true);
        //     DB::beginTransaction();
        //     $model = new Mission();
        //     $model->fill($request['Mission']);
        //     $model->code = -1;
        //     $model->status_id = Mission::REQUESTED_STATUS;
        //     $model->type = Mission::TRANSFER_TYPE;
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }
        //     $code = '';
        //     for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
        //         $code .= '0';
        //     }
        //     $code   =   substr($code, 0, -strlen($model->id));
        //     $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }
        //     foreach ($request->checked_ids as $shipment_id) {
        //         // if ($model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::TRANSFER_TYPE) == 0) {
        //         $shipment = Shipment::find($shipment_id);
        //         $shipment_mission = new ShipmentMission();
        //         $shipment_mission->shipment_id = $shipment->id;
        //         $shipment_mission->mission_id = $model->id;
        //         if ($shipment_mission->save()) {
        //             $shipment->mission_id = $model->id;
        //             $shipment->save();
        //         }
        //         // }
        //     }

        //     //Calaculate Amount
        //     $helper = new TransactionHelper();
        //     $helper->calculate_mission_amount($model->id);


        //     event(new CreateMission($model));
        //     DB::commit();

        //     if ($request->is('api/*')) {
        //         return $model;
        //     } else {
        //         return back()->with(['message_alert' => __('cargo::messages.created')]);
        //     }
        // } catch (\Exception $e) {
        //     DB::rollback();
        //     print_r($e->getMessage());
        //     exit;

        //     flash(translate("Error"))->error();
        //     return back();
        // }
    }

    public function createSupplyMission(Request $request, $type)
    {
        try {
            if (!is_array($request->checked_ids)) {
                $request->checked_ids = json_decode($request->checked_ids, true);
            }
            foreach ($request->checked_ids as $shipment_id) {

                DB::beginTransaction();
                $model = new Mission();
                $model->fill($request['Mission']);
                $model->code = -1;
                $model->status_id = Mission::REQUESTED_STATUS;
                $model->type = Mission::SUPPLY_TYPE;
                if (!$model->save()) {
                    throw new \Exception();
                }
                $code = '';
                for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
                    $code .= '0';
                }
                $code   =   substr($code, 0, -strlen($model->id));
                $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;
                if (!$model->save()) {
                    throw new \Exception();
                }
                if (true) {
                    //$model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::SUPPLY_TYPE) == 0
                    $shipment = Shipment::find($shipment_id);
                    $shipment_mission = new ShipmentMission();
                    $shipment_mission->shipment_id = $shipment->id;
                    $shipment_mission->mission_id = $model->id;
                    if ($shipment_mission->save()) {
                        $shipment->mission_id = $model->id;
                        $shipment->save();
                    }
                }

                //Calaculate Amount
                $helper = new TransactionHelper();
                $helper->calculate_mission_amount($model->id);


                event(new CreateMission($model));

                $shipment_ids = ShipmentMission::where('mission_id', $model->id)->pluck('shipment_id');
                $shipment_data = Shipment::whereIn('id', $shipment_ids)->get();

                foreach ($shipment_data as $shipment) {
                    if ($model->type == Mission::SUPPLY_TYPE  || $model->type == __('cargo::view.supply') || $model->type == __('cargo::view.delivery') || $model->type == Mission::DELIVERY_TYPE) {

                        $address  =   json_decode($shipment->to_area?->name, true)[app()->getLocale()] .  $shipment->reciver_address;

                        Mission::where('id', $model->id)->update([
                            'address' => $address,
                            'area' => $shipment->to_area_id,
                            'client_id' => $shipment->client_id,
                            'created_by' => auth()->user()->id,
                        ]);
                    } else {
                        $address  =   json_decode($shipment->from_area?->name, true)[app()->getLocale()] .  $shipment->client->addressess()->first()?->address;
                        Mission::where('id', $model->id)->update([
                            'address' => $address,
                            'area' => $shipment->from_area_id,
                            'client_id' => $shipment->client_id,
                            'created_by' => auth()->user()->id,

                        ]);
                    }
                }

                DB::commit();
            }
            if ($request->is('api/*')) {
                return $model;
            } else {
                return back()->with(['message_alert' => __('cargo::messages.created')]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
            exit;

            flash(translate("Error"))->error();
            return back();
        }

        // try {
        //     if (!is_array($request->checked_ids)) {
        //         $request->checked_ids = json_decode($request->checked_ids, true);
        //     }

        //     DB::beginTransaction();
        //     $model = new Mission();
        //     $model->fill($request['Mission']);
        //     $model->code = -1;
        //     $model->status_id = Mission::REQUESTED_STATUS;
        //     $model->type = Mission::SUPPLY_TYPE;
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }
        //     $code = '';
        //     for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
        //         $code .= '0';
        //     }
        //     $code   =   substr($code, 0, -strlen($model->id));
        //     $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }
        //     foreach ($request->checked_ids as $shipment_id) {
        //         if ($model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::SUPPLY_TYPE) == 0) {
        //             $shipment = Shipment::find($shipment_id);
        //             $shipment_mission = new ShipmentMission();
        //             $shipment_mission->shipment_id = $shipment->id;
        //             $shipment_mission->mission_id = $model->id;
        //             if ($shipment_mission->save()) {
        //                 $shipment->mission_id = $model->id;
        //                 $shipment->save();
        //             }
        //         }
        //     }

        //     //Calaculate Amount
        //     $helper = new TransactionHelper();
        //     $helper->calculate_mission_amount($model->id);


        //     event(new CreateMission($model));
        //     DB::commit();

        //     if ($request->is('api/*')) {
        //         return $model;
        //     } else {
        //         return back()->with(['message_alert' => __('cargo::messages.created')]);
        //     }
        // } catch (\Exception $e) {
        //     DB::rollback();
        //     print_r($e->getMessage());
        //     exit;

        //     flash(translate("Error"))->error();
        //     return back();
        // }
    }

    public function createReturnMission(Request $request, $type)
    {
        try {
            $request->checked_ids = json_decode($request->checked_ids, true);
            foreach ($request->checked_ids as $shipment_id) {

                DB::beginTransaction();
                $model = new Mission();
                $model->fill($request['Mission']);
                $model->code = -1;
                $model->status_id = Mission::REQUESTED_STATUS;
                $model->otp  = MissionPRNG::get();
                $model->type = Mission::RETURN_TYPE;
                if (!$model->save()) {
                    throw new \Exception();
                }
                $code = '';
                for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
                    $code .= '0';
                }
                $code   =   substr($code, 0, -strlen($model->id));
                $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;
                if (!$model->save()) {
                    throw new \Exception();
                }

                if ($model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::RETURN_TYPE) == 0) {
                    $shipment = Shipment::find($shipment_id);
                    $shipment_mission = new ShipmentMission();
                    $shipment_mission->shipment_id = $shipment->id;
                    $shipment_mission->mission_id = $model->id;
                    if ($shipment_mission->save()) {
                        $shipment->mission_id = $model->id;
                        $shipment->save();
                    }
                }

                //Calaculate Amount
                $helper = new TransactionHelper();
                $helper->calculate_mission_amount($model->id);

                event(new CreateMission($model));

                $shipment_ids = ShipmentMission::where('mission_id', $model->id)->pluck('shipment_id');
                $shipment_data = Shipment::whereIn('id', $shipment_ids)->get();

                foreach ($shipment_data as $shipment) {
                    if ($model->type == Mission::SUPPLY_TYPE  || $model->type == __('cargo::view.supply') || $model->type == __('cargo::view.delivery') || $model->type == Mission::DELIVERY_TYPE) {

                        $address  =   json_decode($shipment->to_area?->name, true)[app()->getLocale()] .  $shipment->reciver_address;

                        Mission::where('id', $model->id)->update([
                            'address' => $address,
                            'area' => $shipment->to_area_id,
                            'client_id' => $shipment->client_id,
                            'created_by' => auth()->user()->id,
                        ]);
                    } else {
                        $address  =   json_decode($shipment->from_area?->name, true)[app()->getLocale()] .  $shipment->client->addressess()->first()?->address;
                        Mission::where('id', $model->id)->update([
                            'address' => $address,
                            'area' => $shipment->from_area_id,
                            'client_id' => $shipment->client_id,
                            'created_by' => auth()->user()->id,

                        ]);
                    }
                }
                DB::commit();
            }

            if ($request->is('api/*')) {
                return $model;
            } else {
                return back()->with(['message_alert' => __('cargo::messages.created')]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
            exit;

            flash(translate("Error"))->error();
            return back();
        }

        // try {
        //     $request->checked_ids = json_decode($request->checked_ids, true);
        //     DB::beginTransaction();
        //     $model = new Mission();
        //     $model->fill($request['Mission']);
        //     $model->code = -1;
        //     $model->status_id = Mission::REQUESTED_STATUS;
        //     $model->otp  = MissionPRNG::get();
        //     $model->type = Mission::RETURN_TYPE;
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }
        //     $code = '';
        //     for ($n = 0; $n < ShipmentSetting::getVal('mission_code_count'); $n++) {
        //         $code .= '0';
        //     }
        //     $code   =   substr($code, 0, -strlen($model->id));
        //     $model->code = ShipmentSetting::getVal('mission_prefix') . $code . $model->id;
        //     if (!$model->save()) {
        //         throw new \Exception();
        //     }

        //     foreach ($request->checked_ids as $shipment_id) {
        //         if ($model->id != null && ShipmentMission::check_if_shipment_is_assigned_to_mission($shipment_id, Mission::RETURN_TYPE) == 0) {
        //             $shipment = Shipment::find($shipment_id);
        //             $shipment_mission = new ShipmentMission();
        //             $shipment_mission->shipment_id = $shipment->id;
        //             $shipment_mission->mission_id = $model->id;
        //             if ($shipment_mission->save()) {
        //                 $shipment->mission_id = $model->id;
        //                 $shipment->save();
        //             }
        //         }
        //     }

        //     //Calaculate Amount
        //     $helper = new TransactionHelper();
        //     $helper->calculate_mission_amount($model->id);

        //     event(new CreateMission($model));
        //     DB::commit();

        //     if ($request->is('api/*')) {
        //         return $model;
        //     } else {
        //         return back()->with(['message_alert' => __('cargo::messages.created')]);
        //     }
        // } catch (\Exception $e) {
        //     DB::rollback();
        //     print_r($e->getMessage());
        //     exit;

        //     flash(translate("Error"))->error();
        //     return back();
        // }
    }

    public function removeShipmentFromMission(Request $request, $fromApi = false)
    {
        $shipment_id = $request->shipment_id;
        $mission_id = $request->mission_id;
        try {
            DB::beginTransaction();

            $mission = Mission::find($mission_id);
            $shipment = Shipment::find($shipment_id);
            if ($mission && $shipment && in_array($mission->status_id, [Mission::APPROVED_STATUS, Mission::REQUESTED_STATUS, Mission::RECIVED_STATUS])) {

                $action = new StatusManagerHelper();
                if ($mission->type == Mission::getType(Mission::PICKUP_TYPE)) {
                    $response = $action->change_shipment_status([$shipment_id], Shipment::SAVED_STATUS, $mission_id);
                } elseif (in_array($mission->type, [Mission::getType(Mission::DELIVERY_TYPE), Mission::getType(Mission::RETURN_TYPE), Mission::getType(Mission::TRANSFER_TYPE)]) && $mission->status_id == Mission::RECIVED_STATUS) {
                    $response = $action->change_shipment_status([$shipment_id], Shipment::RETURNED_STATUS, $mission_id);
                } elseif (in_array($mission->type, [Mission::getType(Mission::DELIVERY_TYPE), Mission::getType(Mission::RETURN_TYPE), Mission::getType(Mission::TRANSFER_TYPE)]) && in_array($mission->status_id, [Mission::APPROVED_STATUS, Mission::REQUESTED_STATUS])) {
                    $response = $action->change_shipment_status([$shipment_id], Shipment::RETURNED_STOCK, $mission_id);
                }

                if ($shipment_mission = $mission->shipment_mission_by_shipment_id($shipment_id)) {
                    $shipment_mission->delete();
                }
                $shipment_reason = new ShipmentReason();
                $shipment_reason->reason_id = $request->reason;
                $shipment_reason->shipment_id = $request->shipment_id;
                $shipment_reason->type = "Delete From Mission";
                $shipment_reason->save();
                //Calaculate Amount
                $helper = new TransactionHelper();
                $helper->calculate_mission_amount($mission_id);

                $mission_shipments = ShipmentMission::where('mission_id', $mission->id)->get();
                if (count($mission_shipments) == 0) {
                    $mission->status_id = Mission::DONE_STATUS;
                    $mission->save();
                }
                event(new UpdateMission($mission_id));
                // event(new ShipmentAction( Shipment::SAVED_STATUS,[$shipment]));
                DB::commit();
                if ($fromApi) {
                    return true;
                }
                return back()->with(['message_alert' => __('cargo::messages.deleted')]);
            } else {
                return back()->with(['error_message_alert' => __('cargo::messages.invalid')]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            print_r($e->getMessage());
            exit;

            flash(translate("Error"))->error();
            return back();
        }
    }

    public function pay($shipment_id)
    {
        $shipment = Shipment::find($shipment_id);
        if (!$shipment || $shipment->paid == 1) {
            flash("Invalid Link")->error();
            return back();
        }

        // return $shipment;
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.pay', compact('shipment'));
    }

    public function ajaxGetEstimationCost(Request $request)
    {
        try {
            $request->validate([
                'total_weight' => 'required|numeric|min:0',
            ]);

            $costs = $this->applyShipmentCostNew($request, $request->package_ids ?? []);
            $formated_cost["tax"] = format_price($costs["tax"]);
            $formated_cost["insurance"] = format_price($costs["insurance"]);

            $formated_cost["return_cost"] = format_price($costs["return_cost"]);
            $formated_cost["shipping_cost"] = format_price($costs["shipping_cost"]);
            $formated_cost["total_cost"] = format_price($costs["shipping_cost"] + $costs["tax"] + $costs["insurance"]);

            return $formated_cost;
        } catch (\Exception $e) {
            \Log::error('Estimation cost error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Error calculating costs',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function applyShipmentCost($request, $packages)
    {
        $client_costs    = Client::where('id', $request['client_id'])->first();

        Shipment::where('id', $request['id'])->update([
            'from_state_id'   => $client_costs?->state_id ?? 0,
            'from_area_id'    => $client_costs?->area_id ?? 0,
        ]);




        $idPackages      = array_column($packages, 'package_id');
        $client_packages = ClientPackage::where('client_id', $request['client_id'])->whereIn('package_id', $idPackages)->get();

        $from_country_id = $request['from_country_id'];
        $to_country_id = $request['to_country_id'];

        if (isset($request['from_state_id']) && isset($request['to_state_id'])) {
            $from_state_id = $request['from_state_id'];
            $to_state_id = $request['to_state_id'];
        }
        if (isset($request['from_area_id']) && isset($request['to_area_id'])) {
            $from_area_id = $request['from_area_id'];
            $to_area_id = $request['to_area_id'];
        }

        $total_weight = 0;
        $package_extras = 0;

        if ($client_packages) {
            foreach ($client_packages as $pack) {
                $total_weight += isset($pack['weight']) ? $pack['weight'] : 1;
                $extra = $pack['cost'];
                $package_extras += $extra;
            }
        } else {
            foreach ($packages as $pack) {
                $total_weight += isset($pack['weight']) ? $pack['weight'] : 1;
                $extra = Package::find($pack['package_id'])->cost;
                $package_extras += $extra;
            }
        }

        //$weight =  $request['total_weight'];
        $weight = isset($request['total_weight']) ? $request['total_weight'] : $total_weight;

        $array = ['return_cost' => 0, 'shipping_cost' => 0, 'tax' => 0, 'insurance' => 0];

        // Shipping Cost = Default + kg + Covered Custom  + Package extra
        $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);

        if (isset($request['from_area_id']) && isset($request['to_area_id'])) {
            $covered_cost = $covered_cost->where('from_area_id', $from_area_id)->where('to_area_id', $to_area_id);
            if (!$covered_cost->first()) {
                $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);

                if (isset($request['from_state_id']) && isset($request['to_state_id'])) {
                    $covered_cost = $covered_cost->where('from_state_id', $from_state_id)->where('to_state_id', $to_state_id);
                    if (!$covered_cost->first()) {
                        $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);
                        $covered_cost = $covered_cost->where('from_state_id', 0)->where('to_state_id', 0);
                    }
                } else {
                    $covered_cost = $covered_cost->where('from_area_id', 0)->where('to_area_id', 0);
                    if (!$covered_cost->first()) {
                        $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);
                        $covered_cost = $covered_cost->where('from_state_id', 0)->where('to_state_id', 0);
                    }
                }
            }
        } else {

            if (isset($request['from_state_id']) && isset($request['to_state_id'])) {
                $covered_cost = $covered_cost->where('from_state_id', $from_state_id)->where('to_state_id', $to_state_id);
            } else {
                $covered_cost = $covered_cost->where('from_area_id', 0)->where('to_area_id', 0);
                if (!$covered_cost->first()) {
                    $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);
                    $covered_cost = $covered_cost->where('from_state_id', 0)->where('to_state_id', 0);
                }
            }
        }
        $covered_cost = $covered_cost->first();

        // Get state-based costs instead of client-based costs
        $state_costs = null;
        if (isset($request['to_state_id']) && $request['to_state_id']) {
            $state_costs = \Modules\Cargo\Entities\State::find($request['to_state_id']);
        }

        $def_return_cost_gram = $state_costs && $state_costs->def_return_cost_gram !== null ? $state_costs->def_return_cost_gram : ShipmentSetting::getCost('def_return_cost_gram');
        $def_return_cost      = $state_costs && $state_costs->def_return_cost !== null ? $state_costs->def_return_cost : ShipmentSetting::getCost('def_return_cost');

        $def_shipping_cost_gram = $state_costs && $state_costs->def_shipping_cost_gram !== null ? $state_costs->def_shipping_cost_gram : ShipmentSetting::getCost('def_shipping_cost_gram');
        $def_shipping_cost      = $state_costs && $state_costs->def_shipping_cost !== null ? $state_costs->def_shipping_cost : ShipmentSetting::getCost('def_shipping_cost');

        // Use global settings for other costs (tax, insurance, mile costs)
        $def_return_mile_cost_gram = ShipmentSetting::getCost('def_return_mile_cost_gram');
        $def_return_mile_cost      = ShipmentSetting::getCost('def_return_mile_cost');
        $def_mile_cost_gram        = ShipmentSetting::getCost('def_mile_cost_gram');
        $def_mile_cost             = ShipmentSetting::getCost('def_mile_cost');
        $def_insurance_gram        = ShipmentSetting::getCost('def_insurance_gram');
        $def_insurance             = ShipmentSetting::getCost('def_insurance');
        $def_tax_gram              = ShipmentSetting::getCost('def_tax_gram');
        $def_tax                   = ShipmentSetting::getCost('def_tax');




        if ($covered_cost != null) {
            if ($weight > 3) {
                if (ShipmentSetting::getVal('is_def_mile_or_fees') == '2') {
                    $return_cost = (float) $def_return_cost ?? $covered_cost->return_cost + (float) ($def_return_cost_gram * ($weight - 3));
                    $shipping_cost_first_one = (float) ($def_shipping_cost != null ? $def_shipping_cost : $covered_cost->shipping_cost) + $package_extras;
                    $shipping_cost_for_extra = (float) ($def_shipping_cost_gram * ($weight - 3));
                } else if (ShipmentSetting::getVal('is_def_mile_or_fees') == '1') {
                    $return_cost = (float) $def_return_mile_cost ?? $covered_cost->return_mile_cost + (float) ($def_return_mile_cost_gram * ($weight - 3));
                    $shipping_cost_first_one = (float) ($def_mile_cost ?? $covered_cost->mile_cost) + $package_extras;
                    $shipping_cost_for_extra = (float) ($def_mile_cost_gram * ($weight - 3));
                }
                $insurance = (float) $def_insurance ?? $covered_cost->insurance + (float) ($def_insurance_gram * ($weight - 3));

                $tax_for_first_one = (($def_tax ?? $covered_cost->tax * $shipping_cost_first_one) / 100);

                $tax_for_exrea = (($def_tax_gram * $shipping_cost_for_extra) / 100);

                $shipping_cost = $shipping_cost_first_one + $shipping_cost_for_extra;
                $tax = $tax_for_first_one + $tax_for_exrea;
            } else {

                if (ShipmentSetting::getVal('is_def_mile_or_fees') == '2') {

                    $return_cost = (float) $def_return_cost ?? $covered_cost->return_cost;
                    $shipping_cost = (float) ($def_shipping_cost != null ? $def_shipping_cost : $covered_cost->shipping_cost) + $package_extras;
                } else if (ShipmentSetting::getVal('is_def_mile_or_fees') == '1') {
                    $return_cost = (float) $def_return_mile_cost ?? $covered_cost->return_mile_cost;
                    $shipping_cost = (float) ($def_mile_cost ?? $covered_cost->mile_cost) + $package_extras;
                }
                $insurance = (float) $def_insurance ?? $covered_cost->insurance;
                $tax = (($def_tax ?? $covered_cost->tax * $shipping_cost) / 100);
            }

            $array['tax'] = $tax;
            $array['insurance'] = $insurance;
            $array['return_cost'] = $return_cost;
            $array['shipping_cost'] = $shipping_cost;
        } else {
            if ($weight > 3) {
                if (ShipmentSetting::getVal('is_def_mile_or_fees') == '2') {
                    $return_cost = $def_return_cost + (float) ($def_return_cost_gram * ($weight - 3));
                    $shipping_cost_first_one = $def_shipping_cost + $package_extras;
                    $shipping_cost_for_extra = (float) ($def_shipping_cost_gram * ($weight - 3));
                } else if (ShipmentSetting::getVal('is_def_mile_or_fees') == '1') {
                    $return_cost = $def_return_mile_cost + (float) ($def_return_mile_cost_gram * ($weight - 3));
                    $shipping_cost_first_one = $def_mile_cost + $package_extras;
                    $shipping_cost_for_extra = (float) ($def_mile_cost_gram * ($weight - 3));
                }

                $insurance = $def_insurance + (float) ($def_insurance_gram * ($weight - 3));
                $tax_for_first_one = (($def_tax * $shipping_cost_first_one) / 100);
                $tax_for_exrea = ((ShipmentSetting::getCost('def_tax_gram') * $shipping_cost_for_extra) / 100);

                $shipping_cost = $shipping_cost_first_one + $shipping_cost_for_extra;
                // dd($shipping_cost_first_one ,$shipping_cost_for_extra  );

                $tax = $tax_for_first_one + $tax_for_exrea;
            } else {
                if (ShipmentSetting::getVal('is_def_mile_or_fees') == '2') {
                    $return_cost = $def_return_cost;
                    $shipping_cost = $def_shipping_cost + $package_extras;
                } else if (ShipmentSetting::getVal('is_def_mile_or_fees') == '1') {
                    $return_cost = $def_return_mile_cost;
                    $shipping_cost = $def_mile_cost + $package_extras;
                }
                $insurance = $def_insurance;
                $tax = (($def_tax * $shipping_cost) / 100);
            }

            $array['tax'] = $tax;
            $array['insurance'] = $insurance;
            $array['return_cost'] = $return_cost;
            $array['shipping_cost'] = $shipping_cost;
        }
        return $array;
    }

    public function print($shipment, $type = 'invoice')
    {
        $shipment = Shipment::find($shipment);
        if ($type == 'label') {
            $adminTheme = env('ADMIN_THEME', 'adminLte');
            return view('cargo::' . $adminTheme . '.pages.shipments.print-label', compact('shipment'));
        } else {
            breadcrumb([
                [
                    'name' => __('cargo::view.dashboard'),
                    'path' => fr_route('admin.dashboard')
                ],
                [
                    'name' => __('cargo::view.shipments'),
                    'path' => fr_route('shipments.index')
                ],
                [
                    'name' => __('cargo::view.shipment') . ' ' . $shipment->code,
                    'path' => fr_route('shipments.show', $shipment->id)
                ],
                [
                    'name' => __('cargo::view.print_invoice'),
                ],
            ]);
            $adminTheme = env('ADMIN_THEME', 'adminLte');
            return view('cargo::' . $adminTheme . '.pages.shipments.print-invoice', compact('shipment'));
        }
    }

    public function printTracking($shipment)
    {

        $shipment = Shipment::find($shipment);
        $client = Client::where('id', $shipment->client_id)->first();
        $PackageShipment = PackageShipment::where('shipment_id', $shipment->id)->get();
        $ClientAddress = ClientAddress::where('client_id', $shipment->client_id)->first();

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.print-tracking')->with(['model' => $shipment, 'client' => $client, 'PackageShipment' => $PackageShipment, 'ClientAddress' => $ClientAddress]);
    }

    public function printStickers(Request $request)
    {
        $request->checked_ids = json_decode($request->checked_ids, true);
        $shipments = Shipment::whereIn('id', $request->checked_ids)->get();
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.print-stickers', compact('shipments'));
    }

    public function sendSMS(Request $request)
    {

        $shipments = Shipment::whereIn('id', $request->ids)
            ->where('sms_is_sent', 0)
            ->get();


        foreach ($shipments as $shipment) {
            $client_name = $shipment->client?->name ?? '';
            $message = "عميل GoSonix العزيز معنا اكتب للمندوب و تابع شحنتك من $client_name حمل تطبيق
             https://play.google.com/store/apps/details?id=com.gosonix.customer.app";
            $phone = $shipment->reciver_phone ?? '';

            if (!empty($phone)) {
                $sent = sendSMSMessage($message, $phone);

                if ($sent) {
                    $shipment->sms_is_sent = 1;
                    $shipment->save();
                }
            }
        }


        return back()->with(['message_alert' => __('sended successfully')]);
    }



    public function ShipmentApis()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.shipment_apis'),
            ],
        ]);
        $client = Client::where('user_id', auth()->user()->id)->first();

        $countries = Country::where('covered', 1)->get();
        $states    = State::where('covered', 1)->get();
        $areas     = Area::get();
        $packages  = Package::all();
        $branches   = Branch::where('is_archived', 0)->get();
        $paymentsGateway = BusinessSetting::where("key", "payment_gateway")->where("value", "1")->get();
        $addresses       = ClientAddress::where('client_id', $client->id)->get();
        $deliveryTimes   = DeliveryTime::all();

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.apis')
            ->with(['countries' => $countries, 'states' => $states, 'areas' => $areas, 'packages' => $packages, 'branches' => $branches, 'paymentsGateway' => $paymentsGateway, 'deliveryTimes' => $deliveryTimes, 'client' => $client, 'addresses' => $addresses]);
    }

    public function ajaxGgenerateToken()
    {
        $userRegistrationHelper = new UserRegistrationHelper(auth()->user()->id);
        $token = $userRegistrationHelper->setApiTokenGenerator();

        return response()->json($token);
    }

    public function createMissionAPI(Request $request)
    {

        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);

        if ($user) {
            $request->validate([
                'checked_ids'       => 'required',
                'type'              => 'required',
                'Mission.client_id' => 'required',
                'Mission.address'   => 'required',
            ]);

            $count = 0;
            foreach ($request->checked_ids as $id) {
                if (Shipment::whereIn('id', $request->checked_ids)->pluck('mission_id')->first()) {
                    $count++;
                }
            }
            if ($count >= 1) {
                return response()->json(['message' => 'this shipment already in mission']);
            } else {
                switch ($request->type) {
                    case Mission::PICKUP_TYPE:
                        $mission = $this->createPickupMission($request, $request->type);
                        break;
                    case Mission::DELIVERY_TYPE:
                        $mission = $this->createDeliveryMission($request, $request->type);
                        break;
                    case Mission::TRANSFER_TYPE:
                        $mission = $this->createTransferMission($request, $request->type);
                        break;
                    case Mission::SUPPLY_TYPE:
                        $mission = $this->createSupplyMission($request, $request->type);
                        break;
                    case Mission::RETURN_TYPE:
                        $mission = $this->createReturnMission($request, $request->type);
                        break;
                }
                return response()->json($mission);
            }
        } else {
            return response()->json(['message' => 'Not Authorized']);
        }
    }

    public function BarcodeScanner()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.barcode_scanner'),
            ],
        ]);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.barcode-scanner');
    }
    public function ChangeStatusByBarcode(Request $request)
    {
        if ($request->checked_ids) {
            $request->checked_ids = json_decode($request->checked_ids, true);
        } else {
            return back()->with(['message_alert' => __('cargo::view.no_shipments_added')]);
        }
        $user_role = auth()->user()->role;
        $action    = new StatusManagerHelper();
        $shipments = Shipment::whereIn('code', $request->checked_ids)->get();

        if (count($shipments) > 0) {
            foreach ($shipments as $shipment) {
                if ($shipment) {
                    $mission = Mission::where('id', $shipment->mission_id)->first();

                    $request->request->add(['ids' => [$shipment->id]]);
                    if ($user_role == 5) { // ROLE 5 == DRIVER

                        if ($shipment->status_id == Shipment::CAPTAIN_ASSIGNED_STATUS) // casa if shipment in delivery mission
                        {
                            $to = Shipment::RECIVED_STATUS;
                            $response = $action->change_shipment_status($request->ids, $to, $mission->id ?? null);
                            if ($response['success']) {
                                event(new ShipmentAction($to, $request->ids));
                            } else {
                                return back()->with(['error_message_alert' => __('cargo::messages.somthing_wrong')]);
                            }
                        } else {
                            $message = __('cargo::view.cant_change_this_shipment') . $shipment->code;
                            return back()->with(['error_message_alert' => $message]);
                        }
                    } elseif (auth()->user()->can('shipments-barcode-scanner') || $user_role == 1) { // ROLE 1 == ADMIN

                        if ($mission && $mission->type == Mission::getType(Mission::PICKUP_TYPE) && $mission->status_id == Mission::RECIVED_STATUS) {
                            // casa if shipment in packup mission
                            $to = Shipment::APPROVED_STATUS;
                            $response = $action->change_shipment_status($request->ids, $to, $mission->id ?? null);
                            if ($response['success']) {
                                event(new ShipmentAction($to, $request->ids));
                            } else {
                                return back()->with(['error_message_alert' => __('cargo::messages.somthing_wrong')]);
                            }
                        } elseif ($shipment->status_id == Shipment::RETURNED_STATUS) {
                            // casa if shipment in returned mission
                            $to = Shipment::RETURNED_STOCK;
                            $response = $action->change_shipment_status($request->ids, $to, $mission->id ?? null);
                            if ($response['success']) {
                                event(new ShipmentAction($to, $request->ids));
                            } else {
                                return back()->with(['error_message_alert' => __('cargo::messages.somthing_wrong')]);
                            }
                        } else {
                            $message = __('cargo::view.cant_change_this_shipment') . $shipment->code;
                            return back()->with(['error_message_alert' => $message]);
                        }
                    }
                } else {
                    $message = __('cargo::view.no_shipment_with_this_barcode') . $shipment->code;
                    return back()->with(['error_message_alert' => $message]);
                }
            }
            return back()->with(['message_alert' => __('cargo::messages.saved')]);
        } else {
            return back()->with(['error_message_alert' => __('cargo::view.no_shipment_with_this_barcode')]);
        }
    }

    public function trackingView(Request $request)
    {
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.tracking-view');
    }

    public function tracking(Request $request)
    {
        if (empty($request->code)) {
            return view('cargo::adminLte.pages.shipments.tracking')->with(['error' => __('cargo::view.enter_your_tracking_code')]);
        }
        $shipment = Shipment::where('code', $request->code)->orWhere('order_id', $request->code)->first();

        if (empty($shipment)) {
            return view('cargo::adminLte.pages.shipments.tracking')->with(['error' => __('cargo::view.error_in_shipment_number')]);
        }
        $client = Client::where('id', $shipment->client_id)->first();
        $PackageShipment = PackageShipment::where('shipment_id', $shipment->id)->get();
        $ClientAddress = ClientAddress::where('client_id', $shipment->client_id)->first();

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        if ($shipment) {
            return view('cargo::' . $adminTheme . '.pages.shipments.tracking')->with(['model' => $shipment, 'client' => $client, 'PackageShipment' => $PackageShipment, 'ClientAddress' => $ClientAddress]);
        } else {
            $error = __('cargo::messages.invalid_code');
            return view('cargo::' . $adminTheme . '.pages.shipments.tracking')->with(['error' => $error]);
        }
    }

    public function delete(Request $request, $id)
    {
        try {
            $shipment = Shipment::find($id);

            if (!$shipment) {
                if ($request->expectsJson()) {
                    return response()->json(['message' => __('cargo::messages.shipment_not_found')], 404);
                }
                return back()->with(['error_message_alert' => __('cargo::messages.shipment_not_found')]);
            }

            $shipment->delete();

            if ($request->expectsJson()) {
                return response()->json(['message' => __('cargo::messages.deleted')]);
            }

            return back()->with(['message_alert' => __('cargo::messages.deleted')]);

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json(['message' => __('cargo::messages.something_wrong')], 500);
            }
            return back()->with(['error_message_alert' => __('cargo::messages.something_wrong')]);
        }
    }

    public function calculator(Request $request)
    {
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.shipments.shipment-calculator');
    }

    public function calculatorStore(Request $request)
    {

        // Normalize phone numbers before validation for guest shipments
        if (isset($request->Shipment['client_phone'])) {
            $request->merge([
                'Shipment' => array_merge($request->Shipment, [
                    'client_phone' => $this->normalizePhoneNumber($request->Shipment['client_phone'])
                ])
            ]);
        }

        if (isset($request->Shipment['reciver_phone'])) {
            $request->merge([
                'Shipment' => array_merge($request->Shipment, [
                    'reciver_phone' => $this->normalizePhoneNumber($request->Shipment['reciver_phone'])
                ])
            ]);
        }

        $request->validate([
            'Shipment.type'            => 'required',
            'Shipment.branch_id'       => 'required',
            'Shipment.client_phone'    => 'required_if:if_have_account,==,0|min:7', // Made more flexible
            'Shipment.reciver_name'    => 'required|string|min:3|max:50',
            'Shipment.reciver_phone'   => 'required|min:7', // Made more flexible
            'Shipment.reciver_address' => 'required|string|min:8',
            'Shipment.from_country_id' => 'required',
            'Shipment.to_country_id'   => 'required',
            'Shipment.from_state_id'   => 'required',
            'Shipment.to_state_id'     => 'required',
            'Shipment.from_area_id'    => 'required',
            'Shipment.to_area_id'      => 'required',
            'Shipment.payment_type'    => 'required',
            'Shipment.payment_method_id' => 'required',
        ]);
        $ClientController = new ClientController(new AclRepository);

        $shipment = $request->Shipment;

        if ($request->if_have_account == '1') {
            $client = Client::where('email', $request->client_email)->first();
            Auth::loginUsingId($client->user_id);
        } elseif ($request->if_have_account == '0') {
            // Add New Client

            $request->request->add(['name' => $request->client_name]);
            $request->request->add(['email' => $request->client_email]);
            $request->request->add(['password' => $request->client_password]);
            $request->request->add(['responsible_mobile' => $request->Shipment['client_phone']]);
            $request->request->add(['responsible_name' => $request->client_name]);
            $request->request->add(['national_id' => $request->national_id ?? '']);
            $request->request->add(['branch_id' => $request->Shipment['branch_id']]);
            $request->request->add(['terms_conditions' => '1']);
            $client = $ClientController->registerStore($request, true);
        }

        if ($client) {
            $shipment['client_id']    = $client->id;
            $shipment['client_phone'] = $client->responsible_mobile;

            // Add New Client Address
            $request->request->add(['client_id' => $client->id]);
            $request->request->add(['address' => $request->client_address]);
            $request->request->add(['country' => $request->Shipment['from_country_id']]);
            $request->request->add(['state'   => $request->Shipment['from_state_id']]);
            if (isset($request->area)) {
                $request->request->add(['area' => $request->Shipment['from_area_id']]);
            }
            $new_address        = $ClientController->addNewAddress($request, $calc = true);
            if ($new_address) {
                $shipment['client_address'] = $new_address->id;
            }
        }
        $request->Shipment = $shipment;
        $model = $this->storeShipment($request);
        return redirect()->route('shipments.show', $model->id)->with(['message_alert' => __('cargo::messages.created')]);
    }

    public function ajaxGetShipmentByBarcode(Request $request)
    {
        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);

        if ($user) {
            $userClient = Client::where('user_id', $user->id)->first();
            $barcode    = $request->barcode;
            $shipment   = Shipment::where('client_id', $userClient->id)->where('barcode', $barcode)->first();
            return response()->json($shipment);
        } else {
            return response()->json(['message' => 'Not Authorized']);
        }
    }

    public function shipmentsReport(ShipmentsDataTable $dataTable, $status = 'all', $type = null)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.shipments_report')
            ]
        ]);

        $data_with = [];
        $share_data = array_merge(get_class_vars(ShipmentsDataTable::class), $data_with);

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::' . $adminTheme . '.pages.shipments.report', $share_data);
    }






    public function showChat($id)
    {
        $CaseManagement = Shipment::findOrFail($id);
        $adminTheme = env('ADMIN_THEME', 'adminLte');

        return view('cargo::' . $adminTheme . '.pages.shipments.chat', compact('id'));
    }

    public function storeMessage(Request $request)
    {
        $request->validate([
            'file' => 'nullable',
            'file.*' => 'mimes:jpg,jpeg,png,pdf,doc,docx,csv,xlsx,xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet|max:40000'
        ]);
        $shipment_id = $request->shipment_id;
        $message = $request->message;
        $CaseManagement = Shipment::findOrFail($shipment_id);
        $created_by = 'admin';

        if (auth()->user()->role == 5) {
            $created_by = 'driver';
        } elseif (auth()->user()->role == 4) {
            $created_by = 'client';
        }

        if (!empty($message) && empty($request->file)) {

            ShipmentChat::create([
                'shipment_id' => $shipment_id,
                'content' => $message,
                'created_by' =>  $created_by,
                'created_by_id' => auth()->id(),
                'content_type' => 'text',
            ]);
        }

        if (!empty($request->file) && empty($message)) {


            $imageAttr = []; // uploadImage($request->file('file'),'case_managements','image','logo');
            $content =   $imageAttr['logo_dir'] . $imageAttr['logo'];

            ShipmentChat::create([
                'shipment_id' => $shipment_id,
                'content' => $content,
                'created_by' =>  $created_by,
                'created_by_id' => auth()->id(),
                'content_type' => 'file',
            ]);

            //notification
            $customer_fcm_token = $CaseManagement->customer->fcm_token ?? " ";

            //  sendNotification($customer_fcm_token , "new message in case number  $CaseManagement->id" , "new message" ) ;
        }

        $case_management_chats = ShipmentChat::where('shipment_id', $shipment_id)->get();
        $html = '';

        //     <div class="msg-img"
        //     style="background-image: url(https://image.flaticon.com/icons/svg/145/145867.svg)">
        // </div>



        foreach ($case_management_chats as $key => $chat) {
            if (auth()->user()->role == 1) {

                ShipmentChat::where('shipment_id', $shipment_id)
                    //->where('is_read', 0)
                    ->update([
                        'admin_is_read' => 1,
                    ]);

                $text =  $chat->content_type == 'text' ? $chat->content : ' <a href="' . $chat->content . '"> <img src="' . $chat->content . '" alt="' . $chat->content . '" width="50px" height="60px"> </a> ';
                $html .=  ' <div class="msg right-msg">


               <div class="msg-bubble">
                   <div class="msg-info">
                       <div class="msg-info-name">' . $chat->userName() . '</div>
                       <div class="msg-info-time">' . $chat->created_at  . '</div>
                   </div>

                   <div class="msg-text">
                     ' .
                    $text
                    . '
                   </div>
               </div>
               </div>';
            } elseif (auth()->user()->role == 4) {
                ShipmentChat::where('shipment_id', $shipment_id)
                    ->update([
                        'provider_is_read' => 1,
                    ]);

                $text =  $chat->content_type == 'text' ? $chat->content : ' <a href="' . $chat->content . '"> <img src="' . $chat->content . '" alt="' . $chat->content . '" width="50px" height="60px"> </a> ';
                $html .=  ' <div class="msg right-msg">


               <div class="msg-bubble">
                   <div class="msg-info">
                       <div class="msg-info-name">' . $chat->userName() . '</div>
                       <div class="msg-info-time">' . $chat->created_at  . '</div>
                   </div>

                   <div class="msg-text">
                     ' .
                    $text
                    . '
                   </div>
               </div>
               </div>';
            } elseif ($chat->created_by == 'driver' &&  auth()->user()->role == 5) {
                ShipmentChat::where('shipment_id', $shipment_id)
                    ->where('created_by', '!=', 'driver')
                    ->where('is_read', 0)
                    ->update([
                        'is_read' => 1,
                    ]);
                $text =  $chat->content_type == 'text' ? $chat->content : ' <a href="' . $chat->content . '"> <img src="' . $chat->content . '" alt="' . $chat->content . '" width="50px" height="60px"> </a> ';
                $html .=  ' <div class="msg right-msg">
               <div class="msg-img"
                   style="background-image: url(https://image.flaticon.com/icons/svg/145/145867.svg)">
               </div>

               <div class="msg-bubble">
                   <div class="msg-info">
                       <div class="msg-info-name">' . $chat->userName() . '</div>
                       <div class="msg-info-time">' . $chat->created_at  . '</div>
                   </div>

                   <div class="msg-text">
                     ' .
                    $text
                    . '
                   </div>
               </div>
               </div>';
            } else {
                $text =  $chat->content_type == 'text' ? $chat->content : ' <a href="' . env('APP_URL') . $chat->content . '"> <img src="' . env('APP_URL') . $chat->content . '" alt="' . env('APP_URL') . $chat->content . '" width="50px" height="60px"> </a> ';
                $html .=  ' <div class="msg left-msg">
                <div class="msg-img"
                    style="background-image: url(https://image.flaticon.com/icons/svg/145/145867.svg)">
                </div>

                <div class="msg-bubble">
                    <div class="msg-info">
                        <div class="msg-info-name">' . $chat->userName() . '</div>
                        <div class="msg-info-time">' . $chat->created_at  . '</div>
                    </div>

                    <div class="msg-text">
                      ' .
                    $text
                    . '
                    </div>
                </div>
                </div>';
            }
        }
        return response()->json(['success' => true, 'data' =>  $html]);
    }


    public function shipmentChatApi(Request $request)
    {
        $request->validate([
            'shipment_id' => 'required',
            'file' => 'nullable',

            'file.*' => 'mimes:jpg,jpeg,png,pdf,doc,docx,csv,xlsx,xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet|max:40000'
        ]);
        $shipment_id = $request->shipment_id;
        $message = $request->message;
        $CaseManagement = Shipment::findOrFail($shipment_id);
        $created_by = 'admin';

        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);
        if (empty($user)) {
            return response()->json(['message' => 'Not Authorized']);
        }

        if ($user->role == 5) {
            $created_by = 'driver';
            ShipmentChat::where('shipment_id', $shipment_id)
                ->where('created_by', '!=', 'driver')
                ->where('is_read', 0)
                ->update([
                    'is_read' => 1,
                ]);
        } elseif ($user->role == 4) {
            $created_by = 'client';
            ShipmentChat::where('shipment_id', $shipment_id)
                ->where('created_by', '!=', 'client')
                ->where('is_read', 0)
                ->update([
                    'is_read' => 1,
                ]);
        }

        if (!empty($message) && empty($request->file)) {

            ShipmentChat::create([
                'shipment_id' => $shipment_id,
                'content' => $message,
                'created_by' =>  $created_by,
                'created_by_id' => $user->id,
                'content_type' => 'text',
            ]);
        }

        if (!empty($request->file) && empty($message)) {


            $imageAttr = []; // uploadImage($request->file('file'),'case_managements','image','logo');
            $content =   $imageAttr['logo_dir'] . $imageAttr['logo'];

            ShipmentChat::create([
                'shipment_id' => $shipment_id,
                'content' => $content,
                'created_by' =>  $created_by,
                'created_by_id' => $user->id,
                'content_type' => 'file',
            ]);

            //notification
            $customer_fcm_token = $CaseManagement->customer->fcm_token ?? " ";

            //  sendNotification($customer_fcm_token , "new message in case number  $CaseManagement->id" , "new message" ) ;
        }

        $case_management_chats = ShipmentChat::where('shipment_id', $shipment_id)->get();


        return response()->json(['success' => true, 'data' => ChatResource::collection($case_management_chats)]);
    }




    public function contactus(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'message' => 'required',

        ]);

        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);
        if (empty($user)) {
            return response()->json(['message' => 'Not Authorized']);
        }

        $created_by = 'client';
        if ($user->role == 5) {
            $created_by = 'driver';
        } elseif ($user->role == 4) {
            $created_by = 'client';
        }

        ContactUs::create([
            'title' => $request->title,
            'message' => $request->message,
            'from' =>  $created_by,
            'creator_id' => $user->id,
        ]);

        return response()->json(['success' => true, 'message' => 'created']);
    }

    /**
     * Apply shipment cost calculation using new region-based cost structure
     * @param $request
     * @param $packages
     * @return array
     */
    public function applyShipmentCostNew($request, $packages)
    {
        try {
            $weight = isset($request['total_weight']) ? $request['total_weight'] : 0;
            $array = ['return_cost' => 0, 'shipping_cost' => 0, 'tax' => 0, 'insurance' => 0];

            // Get state IDs for cost calculation
            $from_state_id = $request['from_state_id'] ?? null;
            $to_state_id = $request['to_state_id'] ?? null;

            // If updating existing shipment, try to get state IDs from shipment
            if (isset($request['id']) && $request['id']) {
                $shipment = Shipment::find($request['id']);
                if ($shipment) {
                    $from_state_id = $from_state_id ?: $shipment->from_state_id;
                    $to_state_id = $to_state_id ?: $shipment->to_state_id;
                }
            }

            // If still no state IDs, try to get from client
            if (!$from_state_id && isset($request['client_id'])) {
                $client = Client::find($request['client_id']);
                if ($client) {
                    $from_state_id = $client->state_id;
                }
            }

            // Get destination state costs (client-specific or default)
            if ($to_state_id) {
                $clientId = $request['client_id'] ?? null;

                // Get client-specific costs if client exists, otherwise use default state costs
                if ($clientId) {
                    $costs = ClientStateCost::getClientStateCosts($clientId, $to_state_id);
                } else {
                    // Fallback to default state costs
                    $to_state = State::find($to_state_id);
                    $costs = [
                        'def_shipping_cost' => $to_state->def_shipping_cost ?? 0,
                        'def_return_cost' => $to_state->def_return_cost ?? 0,
                        'def_shipping_cost_gram' => $to_state->def_shipping_cost_gram ?? 0,
                        'def_return_cost_gram' => $to_state->def_return_cost_gram ?? 0,
                        'source' => 'default_state'
                    ];
                }

                // Calculate shipping cost using client-specific or default costs
                $base_shipping_cost = $costs['def_shipping_cost'] ?: 0;
                $shipping_cost_per_kg = $costs['def_shipping_cost_gram'] ?: 0;

                // Calculate return cost using client-specific or default costs
                $base_return_cost = $costs['def_return_cost'] ?: 0;
                $return_cost_per_kg = $costs['def_return_cost_gram'] ?: 0;

                // Calculate costs (first 3kg base cost + additional weight)
                $base_weight = 3;
                $extra_weight = max(0, $weight - $base_weight);

                $array['shipping_cost'] = $base_shipping_cost + ($extra_weight * $shipping_cost_per_kg);
                $array['return_cost'] = $base_return_cost + ($extra_weight * $return_cost_per_kg);

                // Log cost source for debugging
                \Log::info('Shipment cost calculation', [
                    'client_id' => $clientId,
                    'state_id' => $to_state_id,
                    'cost_source' => $costs['source'] ?? 'unknown',
                    'base_shipping' => $base_shipping_cost,
                    'shipping_per_kg' => $shipping_cost_per_kg,
                    'base_return' => $base_return_cost,
                    'return_per_kg' => $return_cost_per_kg,
                    'weight' => $weight,
                    'calculated_shipping' => $array['shipping_cost'],
                    'calculated_return' => $array['return_cost']
                ]);
            }

            // Add package extra costs if available
            if (!empty($packages)) {
                $package_extra_cost = 0;
                foreach ($packages as $package) {
                    if (isset($package['package_id'])) {
                        $pkg = Package::find($package['package_id']);
                        if ($pkg && $pkg->cost) {
                            $package_extra_cost += $pkg->cost;
                        }
                    }
                }
                $array['shipping_cost'] += $package_extra_cost;
            }

            // Calculate tax and insurance (if needed)
            // You can add tax and insurance calculation here based on your business rules
            $array['tax'] = 0;
            $array['insurance'] = 0;

            return $array;

        } catch (\Exception $e) {
            \Log::error('Cost calculation error: ' . $e->getMessage());
            return ['return_cost' => 0, 'shipping_cost' => 0, 'tax' => 0, 'insurance' => 0];
        }
    }

    /**
     * Normalize phone number to handle old data formats
     * @param string $phone
     * @return string
     */
    private function normalizePhoneNumber($phone)
    {
        if (empty($phone) || trim($phone) === '') {
            return '';
        }

        // Remove all non-numeric characters except +
        $cleanPhone = preg_replace('/[^0-9+]/', '', trim($phone));

        // If empty after cleaning, return empty
        if (empty($cleanPhone)) {
            return '';
        }

        // Handle old data patterns like "************"
        if (strlen($cleanPhone) >= 7) {
            // Remove + if present for processing
            $numericOnly = str_replace('+', '', $cleanPhone);

            // If it's already a valid Egyptian mobile format, return as is
            if (preg_match('/^01[0125][0-9]{8}$/', $numericOnly)) {
                return $numericOnly;
            }

            // If it's 10 digits and doesn't start with 0, try to make it valid
            if (strlen($numericOnly) == 10 && substr($numericOnly, 0, 1) !== '0') {
                // For old data like "1111100000", convert to "01111100000"
                $numericOnly = '0' . $numericOnly;

                // Check if it's now a valid Egyptian format
                if (preg_match('/^01[0125][0-9]{8}$/', $numericOnly)) {
                    return $numericOnly;
                }
            }

            // If it's 11 digits and starts with 0, check if valid
            if (strlen($numericOnly) == 11 && substr($numericOnly, 0, 1) === '0') {
                if (preg_match('/^01[0125][0-9]{8}$/', $numericOnly)) {
                    return $numericOnly;
                }
            }

            // If it starts with country code (20), remove it and add 0
            if (substr($numericOnly, 0, 2) === '20' && strlen($numericOnly) >= 12) {
                $phoneWithoutCountryCode = substr($numericOnly, 2);
                if (strlen($phoneWithoutCountryCode) == 10) {
                    $normalizedPhone = '0' . $phoneWithoutCountryCode;
                    if (preg_match('/^01[0125][0-9]{8}$/', $normalizedPhone)) {
                        return $normalizedPhone;
                    }
                }
            }

            // Return cleaned phone for manual review if nothing else worked
            return $cleanPhone;
        }

        // Return original if too short
        return $phone;
    }

    /**
     * Normalize phone number for validation with country code handling
     * @param string $phone
     * @param string $countryCode
     * @return string
     */
    private function normalizePhoneForValidation($phone, $countryCode = '+20')
    {
        if (empty($phone)) {
            return $phone;
        }

        // Remove all non-numeric characters except +
        $cleanPhone = preg_replace('/[^0-9+]/', '', $phone);

        // If phone already has country code, return as is
        if (substr($cleanPhone, 0, 1) === '+') {
            return $cleanPhone;
        }

        // Remove + from country code for processing
        $countryCodeNumeric = str_replace('+', '', $countryCode);

        // Handle old data patterns like "************"
        if (strlen($cleanPhone) >= 7) {
            // If it's already a valid Egyptian mobile format without country code
            if (preg_match('/^01[0125][0-9]{8}$/', $cleanPhone)) {
                return $countryCode . substr($cleanPhone, 1); // Remove leading 0 and add country code
            }

            // If it's 10 digits and doesn't start with 0, try to make it valid
            if (strlen($cleanPhone) == 10 && substr($cleanPhone, 0, 1) !== '0') {
                // For old data like "1111100000", convert to "+201111100000"
                return $countryCode . $cleanPhone;
            }

            // If it's 11 digits and starts with 0, convert to international format
            if (strlen($cleanPhone) == 11 && substr($cleanPhone, 0, 1) === '0') {
                return $countryCode . substr($cleanPhone, 1);
            }

            // If it's already in international format without +
            if (substr($cleanPhone, 0, strlen($countryCodeNumeric)) === $countryCodeNumeric) {
                return '+' . $cleanPhone;
            }

            // For other cases, add country code
            return $countryCode . $cleanPhone;
        }

        // Return original if too short
        return $phone;
    }
}
