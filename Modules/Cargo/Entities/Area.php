<?php

namespace Modules\Cargo\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Area extends Model
{
    use HasFactory;

    protected $fillable = [];
    protected $guarded = [];
    protected $table = 'areas';

    // Note: Translation support can be added later if needed
    // public $translatable = ['name']; // Columns to translate

    protected static function newFactory()
    {
        return \Modules\Cargo\Database\factories\AreaFactory::new();
    }
    public function state(){
        return $this->belongsTo('Modules\Cargo\Entities\State', 'state_id', 'id');
    }
    public function country(){
        return $this->belongsTo('Modules\Cargo\Entities\Country', 'country_id', 'id');
    }

    /**
     * Search for area by ID or name (supporting both English and Arabic)
     * @param string|int $search
     * @return Area|null
     */
    public static function findByIdOrName($search)
    {
        if (empty($search) || $search === null) {
            return null;
        }

        // First try to find by ID if input is numeric
        if (is_numeric($search)) {
            try {
                $area = self::find($search);
                if ($area) {
                    return $area;
                }
            } catch (\Exception $e) {
                // Continue to name search if ID search fails
            }
        }

        // Search by name (supporting both JSON encoded names and regular names)
        try {
            return self::where(function($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%');

                // Try JSON search only if the database supports it
                try {
                    $query->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(name, '$.en')) LIKE ?", ['%' . $search . '%'])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(name, '$.ar')) LIKE ?", ['%' . $search . '%']);
                } catch (\Exception $e) {
                    // If JSON functions are not supported, just use regular LIKE search
                    // This is already covered by the first where clause
                }
            })->first();
        } catch (\Exception $e) {
            return null;
        }
    }
}
